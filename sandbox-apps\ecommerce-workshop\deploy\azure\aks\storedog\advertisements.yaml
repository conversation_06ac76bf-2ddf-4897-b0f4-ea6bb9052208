apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    service: advertisements
    app: ecommerce
  name: advertisements 
spec:
  replicas: 1
  selector:
    matchLabels:
      service: advertisements
      app: ecommerce
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        service: advertisements
        app: ecommerce
    spec:
      containers:
      - image: ddtraining/advertisements-service:latest
        name: advertisements 
        command: ["ddtrace-run"]
        args: ["flask", "run", "--port=5002", "--host=0.0.0.0"]
        env:
          - name: FLASK_APP
            value: "ads.py"
          - name: FLASK_DEBUG
            value: "1"
          - name: POSTGRES_PASSWORD
            valueFrom:
              secretKeyRef:
                key: pw
                name: db-password
          - name: POSTGRES_USER
            value: "user"
          - name: POSTGRES_HOST
            value: "db"
          - name: DATADOG_SERVICE_NAME
            value: "advertisements-service"
          - name: DD_AGENT_HOST 
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          - name: DD_LOGS_INJECTION
            value: "true"
          - name: DD_ANALYTICS_ENABLED
            value: "true"
        ports:
        - containerPort: 5002
        resources: {}
---
apiVersion: v1
kind: Service
metadata:
  labels:
    service: advertisements
    app: ecommerce
  name: advertisements
spec:
  ports:
  - port: 5002
    protocol: TCP
    targetPort: 5002
  selector:
    service: advertisements
    app: ecommerce
status:
