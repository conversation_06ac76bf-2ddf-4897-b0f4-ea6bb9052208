# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component
resources:
- service-account-adservice.yaml
- service-account-cartservice.yaml
- service-account-checkoutservice.yaml
- service-account-currencyservice.yaml
- service-account-emailservice.yaml
- service-account-frontend.yaml
- service-account-loadgenerator.yaml
- service-account-paymentservice.yaml
- service-account-productcatalogservice.yaml
- service-account-recommendationservice.yaml
- service-account-shippingservice.yaml
patchesJson6902:
- target:
    kind: Deployment
    name: adservice
  patch: |-
    - op: replace
      path: /spec/template/spec/serviceAccountName
      value: adservice
- target:
    kind: Deployment
    name: cartservice
  patch: |-
    - op: replace
      path: /spec/template/spec/serviceAccountName
      value: cartservice
- target:
    kind: Deployment
    name: checkoutservice
  patch: |-
    - op: replace
      path: /spec/template/spec/serviceAccountName
      value: checkoutservice
- target:
    kind: Deployment
    name: currencyservice
  patch: |-
    - op: replace
      path: /spec/template/spec/serviceAccountName
      value: currencyservice
- target:
    kind: Deployment
    name: emailservice
  patch: |-
    - op: replace
      path: /spec/template/spec/serviceAccountName
      value: emailservice
- target:
    kind: Deployment
    name: frontend
  patch: |-
    - op: replace
      path: /spec/template/spec/serviceAccountName
      value: frontend
- target:
    kind: Deployment
    name: loadgenerator
  patch: |-
    - op: replace
      path: /spec/template/spec/serviceAccountName
      value: loadgenerator
- target:
    kind: Deployment
    name: paymentservice
  patch: |-
    - op: replace
      path: /spec/template/spec/serviceAccountName
      value: paymentservice
- target:
    kind: Deployment
    name: productcatalogservice
  patch: |-
    - op: replace
      path: /spec/template/spec/serviceAccountName
      value: productcatalogservice
- target:
    kind: Deployment
    name: recommendationservice
  patch: |-
    - op: replace
      path: /spec/template/spec/serviceAccountName
      value: recommendationservice
- target:
    kind: Deployment
    name: shippingservice
  patch: |-
    - op: replace
      path: /spec/template/spec/serviceAccountName
      value: shippingservice