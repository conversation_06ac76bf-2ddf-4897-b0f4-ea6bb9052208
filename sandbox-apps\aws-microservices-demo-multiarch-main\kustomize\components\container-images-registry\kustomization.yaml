# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component
images:
- name: gcr.io/google-samples/microservices-demo/adservice
  newName: CONTAINER_IMAGES_REGISTRY/adservice
- name: gcr.io/google-samples/microservices-demo/cartservice
  newName: CONTAINER_IMAGES_REGISTRY/cartservice
- name: gcr.io/google-samples/microservices-demo/checkoutservice
  newName: CONTAINER_IMAGES_REGISTRY/checkoutservice
- name: gcr.io/google-samples/microservices-demo/currencyservice
  newName: CONTAINER_IMAGES_REGISTRY/currencyservice
- name: gcr.io/google-samples/microservices-demo/emailservice
  newName: CONTAINER_IMAGES_REGISTRY/emailservice
- name: gcr.io/google-samples/microservices-demo/frontend
  newName: CONTAINER_IMAGES_REGISTRY/frontend
- name: gcr.io/google-samples/microservices-demo/loadgenerator
  newName: CONTAINER_IMAGES_REGISTRY/loadgenerator
- name: gcr.io/google-samples/microservices-demo/paymentservice
  newName: CONTAINER_IMAGES_REGISTRY/paymentservice
- name: gcr.io/google-samples/microservices-demo/productcatalogservice
  newName: CONTAINER_IMAGES_REGISTRY/productcatalogservice
- name: gcr.io/google-samples/microservices-demo/recommendationservice
  newName: CONTAINER_IMAGES_REGISTRY/recommendationservice
- name: gcr.io/google-samples/microservices-demo/shippingservice
  newName: CONTAINER_IMAGES_REGISTRY/shippingservice
- name: redis
  newName: CONTAINER_IMAGES_REGISTRY/redis
