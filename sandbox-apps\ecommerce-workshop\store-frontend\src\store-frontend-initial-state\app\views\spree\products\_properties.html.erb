<% unless @product_properties.blank? %>
  <h3 class="product-section-title h4 mt-4">
    <%= Spree.t('properties')%>
  </h3>

  <table id="product-properties" class="table table-striped" data-hook>
    <tbody>
      <% @product_properties.each do |product_property| %>
        <% css_class = cycle('even', 'odd', name: "properties") %>
        <tr class="<%= css_class %>">
          <td>
            <strong>
              <%= product_property.property.presentation %>
            </strong>
          </td>
          <td><%= product_property.value %></td>
        </tr>
      <% end %>
      <% reset_cycle('properties') %>
    </tbody>
  </table>
<% end %>
