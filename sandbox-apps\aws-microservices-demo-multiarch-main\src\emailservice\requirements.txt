#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile --output-file=requirements.txt requirements.in
#
backoff==2.2.1
    # via opentelemetry-exporter-otlp-proto-grpc
cachetools==5.2.0
    # via google-auth
certifi==2022.12.7
    # via requests
charset-normalizer==2.1.1
    # via requests
deprecated==1.2.13
    # via opentelemetry-api
google-api-core[grpc]==2.11.0
    # via
    #   -r requirements.in
    #   google-api-python-client
    #   google-cloud-trace
google-api-python-client==2.70.0
    # via google-cloud-profiler
google-auth==2.15.0
    # via
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-cloud-profiler
google-auth-httplib2==0.1.0
    # via
    #   google-api-python-client
    #   google-cloud-profiler
google-cloud-profiler==4.0.0
    # via -r requirements.in
google-cloud-trace==1.8.0
    # via -r requirements.in
googleapis-common-protos==1.57.0
    # via
    #   google-api-core
    #   grpcio-status
    #   opentelemetry-exporter-otlp-proto-grpc
grpcio==1.51.1
    # via
    #   -r requirements.in
    #   google-api-core
    #   grpcio-health-checking
    #   grpcio-status
    #   opentelemetry-exporter-otlp-proto-grpc
grpcio-health-checking==1.51.1
    # via -r requirements.in
grpcio-status==1.51.1
    # via google-api-core
httplib2==0.21.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
idna==3.4
    # via requests
jinja2==3.1.2
    # via -r requirements.in
markupsafe==2.1.1
    # via jinja2
opentelemetry-api==1.15.0
    # via
    #   opentelemetry-distro
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-grpc
    #   opentelemetry-sdk
opentelemetry-distro==0.36b0
    # via -r requirements.in
opentelemetry-exporter-otlp-proto-grpc==1.15.0
    # via -r requirements.in
opentelemetry-instrumentation==0.36b0
    # via
    #   opentelemetry-distro
    #   opentelemetry-instrumentation-grpc
opentelemetry-instrumentation-grpc==0.36b0
    # via -r requirements.in
opentelemetry-proto==1.15.0
    # via opentelemetry-exporter-otlp-proto-grpc
opentelemetry-sdk==1.15.0
    # via
    #   opentelemetry-distro
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-instrumentation-grpc
opentelemetry-semantic-conventions==0.36b0
    # via
    #   opentelemetry-instrumentation-grpc
    #   opentelemetry-sdk
proto-plus==1.22.1
    # via google-cloud-trace
protobuf==4.21.12
    # via
    #   -r requirements.in
    #   google-api-core
    #   google-cloud-profiler
    #   google-cloud-trace
    #   googleapis-common-protos
    #   grpcio-health-checking
    #   grpcio-status
    #   opentelemetry-proto
    #   proto-plus
pyasn1==0.4.8
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.2.8
    # via google-auth
pyparsing==3.0.9
    # via httplib2
python-json-logger==2.0.4
    # via -r requirements.in
requests==2.28.1
    # via
    #   -r requirements.in
    #   google-api-core
    #   google-cloud-profiler
rsa==4.9
    # via google-auth
six==1.16.0
    # via
    #   google-auth
    #   google-auth-httplib2
typing-extensions==4.4.0
    # via opentelemetry-sdk
uritemplate==4.1.1
    # via google-api-python-client
urllib3==1.26.13
    # via requests
wrapt==1.14.1
    # via
    #   deprecated
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-grpc

# The following packages are considered to be unsafe in a requirements file:
# setuptools
