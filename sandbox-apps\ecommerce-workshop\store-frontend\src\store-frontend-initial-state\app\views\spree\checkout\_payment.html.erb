<div class="card" id="payment" data-hook>
  <div class="card-header">
    <h3 class="card-title mb-0 h5">
      <%= Spree.t(:payment_information) %>
    </h3>
  </div>

  <div class="card-body" data-hook="checkout_payment_step">
    <% if @payment_sources.present? %>
      <div class="card_options">
        <%= radio_button_tag 'use_existing_card', 'yes', true %>
        <label for="use_existing_card_yes">
          <%= Spree.t(:use_existing_cc) %>
        </label>
        <%= radio_button_tag 'use_existing_card', 'no' %>
        <label for="use_existing_card_no">
          <%= Spree.t(:use_new_cc_or_payment_method) %>
        </label>
      </div>

      <div id="existing_cards">
        <p class="form-group" data-hook="existing_cards">
          <table class="existing-credit-card-list">
            <tbody>
              <% @payment_sources.each do |card| %>
                <tr id="<%= dom_id(card,'spree')%>" class="<%= cycle('even', 'odd') %>">
                  <td>
                    <%= radio_button_tag "order[existing_card]", card.id, (card == @payment_sources.first), { class: "existing-cc-radio" }  %>
                  </td>
                  <td><%= card.name %></td>
                  <td><%= card.display_number %></td>
                  <td><%= card.month %> / <%= card.year %></td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </p>
      </div>
    <% end %>

    <%= render partial: 'spree/checkout/payment/storecredit' %>

    <ul class="list-group" id="payment-method-fields" data-hook>
      <% @order.available_payment_methods.each do |method| %>
        <li class="list-group-item radio">
          <label>
            <%= radio_button_tag "order[payments_attributes][][payment_method_id]", method.id, method == @order.available_payment_methods.first %>
            <%= Spree.t(method.name, scope: :payment_methods, default: method.name) %>
          </label>
        </li>
      <% end %>
    </ul>

    <ul id="payment-methods" class="list-unstyled position-relative" data-hook>
      <% @order.available_payment_methods.each do |method| %>
        <li id="payment_method_<%= method.id %>" class="<%= 'last' if method == @order.available_payment_methods.last %>" data-hook>
          <fieldset>
            <%= render partial: "spree/checkout/payment/#{method.method_type}", locals: { payment_method: method } %>
          </fieldset>
        </li>
      <% end %>
    </ul>


    <% if Spree::Frontend::Config[:coupon_codes_enabled] %>
      <p class='field mb-0' data-hook='coupon_code'>
        <%= form.label :coupon_code %>
        <%= form.text_field :coupon_code, class: 'form-control' %>
      </p>
    <% end %>
  </div>
</div>

<div class="card text-right form-buttons my-4" data-hook="buttons">
  <div class="card-body">
    <%= submit_tag Spree.t(:save_and_continue), class: 'btn btn-lg btn-success primary' %>
    <script>Spree.disableSaveOnClick();</script>
  </div>
</div>
