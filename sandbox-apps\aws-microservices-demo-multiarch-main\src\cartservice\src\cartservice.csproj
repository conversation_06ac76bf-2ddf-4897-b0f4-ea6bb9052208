<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Grpc.AspNetCore" Version="2.50.0" />
    <PackageReference Include="Grpc.HealthCheck" Version="2.50.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="7.0.1" />
    <!-- Add explicit direct dependency required for ipv6, see https://github.com/dotnet/aspnetcore/issues/45424 -->
    <PackageReference Include="StackExchange.Redis" Version="2.6.86" />
    <PackageReference Include="Google.Cloud.Spanner.Data" Version="4.2.0" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="protos\Cart.proto" GrpcServices="Both" />
  </ItemGroup>
</Project>
