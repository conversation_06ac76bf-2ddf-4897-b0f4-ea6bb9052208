version: '3'
services:
  agent:
    image: "datadog/agent:7.29.0"
    environment:
      - DD_API_KEY
      - DD_PROCESS_AGENT_ENABLED=true
      - DD_TAGS='env:development'
# add agent env variables
# add agent trace port
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /proc/:/host/proc/:ro
      - /sys/fs/cgroup/:/host/sys/fs/cgroup:ro
# add agent log labels
  discounts:
    environment:
      - FLASK_APP=discounts.py
      - FLASK_DEBUG=1
      - POSTGRES_PASSWORD
      - POSTGRES_USER
      - POSTGRES_HOST=db
      - DATADOG_SERVICE_NAME=discounts-service
# add discounts env variables
    image: "ddtraining/discounts:2.1.0"
    command: flask run --port=5001 --host=0.0.0.0
    ports:
      - "5001:5001"
    volumes:
      - "../../discounts-service:/app"
    depends_on:
      - agent
      - db
# add discounts log labels
  frontend:
    environment:
      - DB_USERNAME
      - DB_PASSWORD
# add frontend env variables
    image: "ddtraining/storefront:2.1.0"
    ports:
      - "3000:3000"
    depends_on:
      - agent
      - db
      - discounts
      - advertisements
# add frontend log labels
  advertisements:
    environment:
      - FLASK_APP=ads.py
      - FLASK_DEBUG=1
      - POSTGRES_PASSWORD
      - POSTGRES_USER
      - POSTGRES_HOST=db
      - DATADOG_SERVICE_NAME=advertisements-service
# add ads env variables
    image: "ddtraining/advertisements:2.1.0"
    command: flask run --port=5002 --host=0.0.0.0
    ports:
      - "5002:5002"
    volumes:
      - "../../ads-service:/app"
    depends_on:
      - agent
      - db
# add ads log labels
  db:
    image: postgres:11-alpine
    restart: always
    environment:
      - POSTGRES_PASSWORD
      - POSTGRES_USER
    labels:
      com.datadoghq.ad.logs: '[{"source": "postgres", "service": "postgres"}]'
