<%# Non-link tag that stands for skipped pages...
  - available local variables
    current_page:  a page object for the currently displayed page
    total_pages:   total number of pages
    per_page:      number of items to fetch per page
    remote:        data-remote
-%>
<li class="page gap disabled page-item">
  <a href="#" onclick="return false;" class="page-link">
    <%= raw(t 'views.pagination.truncate') %>
  </a>
</li>
