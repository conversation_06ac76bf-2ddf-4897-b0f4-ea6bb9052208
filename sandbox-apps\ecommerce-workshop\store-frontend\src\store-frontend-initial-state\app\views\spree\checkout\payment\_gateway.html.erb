<div class="card mt-4 w-100">
  <div class="card-body">
    <%= image_tag 'credit_cards/credit_card.gif',
      id: 'credit-card-image',
      width: '170',
      height: '28',
      class: 'position-absolute',
      style: 'right: 1rem' %>

    <% param_prefix = "payment_source[#{payment_method.id}]" %>

    <p class="field">
      <%= label_tag "name_on_card_#{payment_method.id}" do %>
        <%= Spree.t(:name_on_card) %><abbr class="required" title="required">*</abbr>
      <% end %>
      <%= text_field_tag "#{param_prefix}[name]", "#{@order.bill_address_firstname} #{@order.bill_address_lastname}", { id: "name_on_card_#{payment_method.id}", class: 'form-control required'} %>
    </p>

    <p class="field" data-hook="card_number">
      <%= label_tag "card_number" do %>
        <%= Spree.t(:card_number) %><abbr class="required" title="required">*</abbr>
      <% end %>
      <% options_hash = Rails.env.production? ? {autocomplete: 'off'} : {} %>
      <%= text_field_tag "#{param_prefix}[number]", '', options_hash.merge(id: 'card_number', class: 'form-control required cardNumber', size: 19, maxlength: 19, autocomplete: "off") %>
      &nbsp;
      <span id="card_type" style="display:none;">
        ( <span id="looks_like" ><%= Spree.t(:card_type_is) %> <span id="type"></span></span>
          <span id="unrecognized"><%= Spree.t(:unrecognized_card_type) %></span>
        )
      </span>
    </p>
    <div class="row">
      <div class="col-md-8 field" data-hook="card_expiration">
        <%= label_tag "card_expiry" do %>
          <%= Spree.t(:expiration) %><abbr class="required" title="required">*</abbr>
        <% end %>
        <%= text_field_tag "#{param_prefix}[expiry]", '', id: 'card_expiry', class: "form-control required cardExpiry", placeholder: "MM / YY" %>
      </div>
      <div class="col-md-4 field" data-hook="card_code">
        <%= label_tag "card_code" do %>
          <%= Spree.t(:card_code) %><abbr class="required" title="required">*</abbr>
        <% end %>
        <%= text_field_tag "#{param_prefix}[verification_value]", '', options_hash.merge(id: 'card_code', class: 'form-control required cardCode', size: 5) %>
        <%= link_to Spree.t(:what_is_this), spree.cvv_path, target: :blank, "data-hook" => "cvv_link", id: "cvv_link" %>
      </div>
    </div>

    <%= hidden_field_tag "#{param_prefix}[cc_type]", '', id: "cc_type", class: 'ccType' %>
  </div>
</div>
