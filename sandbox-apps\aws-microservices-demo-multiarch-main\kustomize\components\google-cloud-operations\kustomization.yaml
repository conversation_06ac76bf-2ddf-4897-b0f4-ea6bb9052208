# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component
resources:
  - otel-collector.yaml
patchesStrategicMerge:
# adservice - not yet implemented
# checkoutservice - tracing, profiler
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: checkoutservice
  spec:
    template:
      spec:
        containers:
          - name: server
            env:
            - name: COLLECTOR_SERVICE_ADDR
              value: "opentelemetrycollector:4317"
            - name: ENABLE_TRACING
              value: "1"
            - name: ENABLE_PROFILER
              value: "1"
# currencyservice - tracing, profiler
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: currencyservice
  spec:
    template:
      spec:
        containers:
          - name: server
            env:
            - name: COLLECTOR_SERVICE_ADDR
              value: "opentelemetrycollector:4317"
            - name: ENABLE_TRACING
              value: "1"
            - name: DISABLE_PROFILER
              $patch: delete
# emailservice - tracing, profiler
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: emailservice
  spec:
    template:
      spec:
        containers:
          - name: server
            env:
            - name: COLLECTOR_SERVICE_ADDR
              value: "opentelemetrycollector:4317"
            - name: ENABLE_TRACING
              value: "1"
            - name: DISABLE_PROFILER
              $patch: delete
# frontend - tracing, profiler
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: frontend
  spec:
    template:
      spec:
        containers:
          - name: server
            env:
            - name: ENABLE_TRACING
              value: "1"
            - name: COLLECTOR_SERVICE_ADDR
              value: "opentelemetrycollector:4317"
            - name: ENABLE_PROFILER
              value: "1"
# paymentservice - tracing, profiler
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: paymentservice
  spec:
    template:
      spec:
        containers:
          - name: server
            env:
            - name: COLLECTOR_SERVICE_ADDR
              value: "opentelemetrycollector:4317"
            - name: ENABLE_TRACING
              value: "1"
            - name: DISABLE_PROFILER
              $patch: delete
# productcatalogservice - tracing, profiler
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: productcatalogservice
  spec:
    template:
      spec:
        containers:
          - name: server
            env:
            - name: COLLECTOR_SERVICE_ADDR
              value: "opentelemetrycollector:4317"
            - name: ENABLE_TRACING
              value: "1"
            - name: DISABLE_PROFILER
              value: "1"
# recommendationservice - tracing, profiler
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: recommendationservice
  spec:
    template:
      spec:
        containers:
          - name: server
            env:
            - name: COLLECTOR_SERVICE_ADDR
              value: "opentelemetrycollector:4317"
            - name: ENABLE_TRACING
              value: "1"
            - name: DISABLE_PROFILER
              $patch: delete
# shippingservice - stats, tracing, profiler
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: shippingservice
  spec:
    template:
      spec:
        containers:
          - name: server
            env:
            - name: DISABLE_PROFILER
              $patch: delete
