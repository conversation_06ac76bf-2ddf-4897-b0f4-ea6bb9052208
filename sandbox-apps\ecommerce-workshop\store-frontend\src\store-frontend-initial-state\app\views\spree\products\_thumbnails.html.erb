<%# no need for thumbnails unless there is more than one image %>
<% if (@product.images + @product.variant_images).uniq.size > 1 %>
  <ul id="product-thumbnails" class="thumbnails d-flex w-100 pl-0 my-4" data-hook>
    <% @product.images.each do |i| %>
      <li class='tmb-all mr-2 tmb-<%= i.viewable.id %>'>
        <%= link_to(image_tag(main_app.url_for(i.url(:mini)), class: "img-thumbnail d-inline-block"), main_app.url_for(i.url(:product))) %>
      </li>
    <% end %>

    <% if @product.has_variants? %>
      <% @product.variant_images.each do |i| %>
        <% next if @product.images.include?(i) %>
        <li class='vtmb mr-2 tmb-<%= i.viewable.id %>'>
          <%= link_to(image_tag(main_app.url_for(i.url(:mini)), class: "img-thumbnail"), main_app.url_for(i.url(:product))) %>
        </li>
      <% end %>
    <% end %>
  </ul>
<% end %>
