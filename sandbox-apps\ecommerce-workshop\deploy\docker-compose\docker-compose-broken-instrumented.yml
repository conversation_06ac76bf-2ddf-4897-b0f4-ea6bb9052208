version: '3'
services:
  agent:
    image: "datadog/agent:7.29.0"
    environment:
      - DD_API_KEY
      - DD_APM_ENABLED=true
      - DD_LOGS_ENABLED=true
      - DD_LOGS_CONFIG_CONTAINER_COLLECT_ALL=true
      - DD_PROCESS_AGENT_ENABLED=true
      - DD_TAGS='env:development'
    ports:
      - "8126:8126"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /proc/:/host/proc/:ro
      - /sys/fs/cgroup/:/host/sys/fs/cgroup:ro
    labels:
      com.datadoghq.ad.logs: '[{"source": "agent", "service": "agent"}]'
  discounts:
    environment:
      - FLASK_DEBUG=1
      - POSTGRES_PASSWORD
      - POSTGRES_USER
      - POSTGRES_HOST=db
      - DD_SERVICE=discounts-service
      - DD_AGENT_HOST=agent
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DD_PROFILING_ENABLED=true
      - DD_VERSION=1.0
    image: "ddtraining/discounts:2.1.0"
    ports:
      - "5001:5001"
    volumes:
      - "../../discounts-service:/app"
    depends_on:
      - agent
      - db
    labels:
      com.datadoghq.ad.logs: '[{"source": "python", "service": "discounts-service"}]'
  frontend:
    environment:
      - DD_AGENT_HOST=agent
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DD_SERVICE=store-frontend
      - DD_VERSION=1.0
      - DB_USERNAME
      - DB_PASSWORD
      - DD_CLIENT_TOKEN
      - DD_APPLICATION_ID
      - DD_ENV=development
      - DD_SITE=datadoghq.com
    image: "ddtraining/storefront:2.1.0"
    command: sh docker-entrypoint.sh
    ports:
      - "3000:3000"
    depends_on:
      - agent
      - db
      - discounts
      - advertisements
    labels:
      com.datadoghq.ad.logs: '[{"source": "ruby", "service": "store-frontend"}]'
  advertisements:
    environment:
      - FLASK_DEBUG=1
      - POSTGRES_PASSWORD
      - POSTGRES_USER
      - POSTGRES_HOST=db
      - DD_SERVICE=advertisements-service
      - DD_AGENT_HOST=agent
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DD_PROFILING_ENABLED=true
      - DD_VERSION=1.0
    image: "ddtraining/advertisements:2.1.0"
    ports:
      - "5002:5002"
    volumes:
      - "../../ads-service:/app"
    depends_on:
      - agent
      - db
    labels:
      com.datadoghq.ad.logs: '[{"source": "python", "service": "ads-service"}]'
  db:
    image: postgres:11-alpine
    restart: always
    environment:
      - POSTGRES_PASSWORD
      - POSTGRES_USER
      - DD_ENV=development
    labels:
      com.datadoghq.ad.logs: '[{"source": "postgresql", "service": "postgres"}]'
