{{- if .Values.paymentService.create }}
{{- if .Values.serviceAccounts.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.paymentService.name }}
  namespace: {{.Release.Namespace}}
  {{- if not .Values.serviceAccounts.annotationsOnlyForCartservice }}
  {{- with .Values.serviceAccounts.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
---
{{- end }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.paymentService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.paymentService.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.paymentService.name }}
    spec:
      {{- if .Values.serviceAccounts.create }}
      serviceAccountName: {{ .Values.paymentService.name }}
      {{- else }}
      serviceAccountName: default
      {{- end }}
      terminationGracePeriodSeconds: 5
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 1000
        {{- if .Values.seccompProfile.enable }}
        seccompProfile:
          type: {{ .Values.seccompProfile.type }}
        {{- end }}
      containers:
      - name: server
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - all
          privileged: false
          readOnlyRootFilesystem: true
        {{- if .Values.nativeGrpcHealthCheck }}
        image: {{ .Values.images.repository }}/{{ .Values.paymentService.name }}:{{ .Values.images.tag | default .Chart.AppVersion }}-native-grpc-probes
        {{- else }}
        image: {{ .Values.images.repository }}/{{ .Values.paymentService.name }}:{{ .Values.images.tag | default .Chart.AppVersion }}
        {{- end }}
        ports:
        - containerPort: 50051
        env:
        - name: PORT
          value: "50051"
        {{- if .Values.opentelemetryCollector.create }}
        - name: COLLECTOR_SERVICE_ADDR
          value: "{{ .Values.opentelemetryCollector.name }}:4317"
        {{- end }}
        {{- if .Values.googleCloudOperations.tracing }}
        - name: ENABLE_TRACING
          value: "1"
        {{- end }}
        {{- if not .Values.googleCloudOperations.profiler }}
        - name: DISABLE_PROFILER
          value: "1"
        {{- end }}
        readinessProbe:
          {{- if .Values.nativeGrpcHealthCheck }}
          grpc:
            port: 50051
          {{- else }}
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:50051"]
          {{- end }}
        livenessProbe:
          {{- if .Values.nativeGrpcHealthCheck }}
          grpc:
            port: 50051
          {{- else }}
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:50051"]
          {{- end }}
        resources:
          requests:
            cpu: 100m
            memory: 64Mi
          limits:
            cpu: 200m
            memory: 128Mi
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.paymentService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  type: ClusterIP
  selector:
    app: {{ .Values.paymentService.name }}
  ports:
  - name: grpc
    port: 50051
    targetPort: 50051
{{- if .Values.networkPolicies.create }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ .Values.paymentService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  podSelector:
    matchLabels:
      app: {{ .Values.paymentService.name }}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: {{ .Values.checkoutService.name }}
    ports:
     - port: 50051
       protocol: TCP
  egress:
  - {}
{{- end }}
{{- if .Values.sidecars.create }}
---
apiVersion: networking.istio.io/v1beta1
kind: Sidecar
metadata:
  name: {{ .Values.paymentService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  workloadSelector:
    labels:
      app: {{ .Values.paymentService.name }}
  egress:
  - hosts:
    - istio-system/*
    {{- if .Values.opentelemetryCollector.create }}
    - ./{{ .Values.opentelemetryCollector.name }}.{{ .Release.Namespace }}.svc.cluster.local
    {{- end }}
{{- end }}
{{- if .Values.authorizationPolicies.create }}
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ .Values.paymentService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.paymentService.name }}
  rules:
  - from:
    - source:
        principals:
        {{- if .Values.serviceAccounts.create }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.checkoutService.name }}
        {{- else }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/default
        {{- end }}
    to:
    - operation:
        paths:
        - /hipstershop.PaymentService/Charge
        methods:
        - POST
        ports:
        - "50051"
{{- end }}
{{- end }}
