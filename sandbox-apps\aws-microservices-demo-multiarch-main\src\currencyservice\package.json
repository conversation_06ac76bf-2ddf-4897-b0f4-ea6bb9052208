{"name": "grpc-currency-service", "version": "0.1.0", "description": "A gRPC currency conversion microservice", "repository": "https://github.com/GoogleCloudPlatform/microservices-demo", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "license": "Apache-2.0", "dependencies": {"@google-cloud/profiler": "5.0.3", "@google-cloud/trace-agent": "7.1.2", "@grpc/grpc-js": "1.8.0", "@grpc/proto-loader": "0.7.4", "async": "3.2.4", "google-protobuf": "3.21.2", "@opentelemetry/api": "1.3.0", "@opentelemetry/exporter-otlp-grpc": "0.26.0", "@opentelemetry/instrumentation-grpc": "0.34.0", "@opentelemetry/sdk-trace-base": "1.8.0", "@opentelemetry/sdk-node": "0.34.0", "pino": "8.8.0", "xml2js": "0.4.23"}}