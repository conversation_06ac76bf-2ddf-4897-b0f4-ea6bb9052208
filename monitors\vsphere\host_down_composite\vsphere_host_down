{"name": "vSphere ESX Host {{host.name}} is DOWN", "type": "metric alert", "query": "max(last_5m):default_zero(min:vsphere.sys.uptime.latest{vsphere_type:host*} by {host,vcenter_server}) <= 0", "message": "{{#is_alert}}\nvSphere ESX Host {{host.name}} is DOWN\n\nPlease verify that the ESX host is up and running.\n\n{{/is_alert}}\n\n{{#is_alert_recovery}}\nvSphere ESX Host {{host.name}} is responding again!\n{{/is_alert_recovery}}", "tags": [], "options": {"notify_audit": false, "locked": false, "timeout_h": 0, "silenced": {}, "include_tags": true, "no_data_timeframe": null, "require_full_window": true, "notify_no_data": false, "renotify_interval": 0, "new_group_delay": 60, "thresholds": {"critical": 0}, "escalation_message": ""}, "priority": null, "classification": "integration"}