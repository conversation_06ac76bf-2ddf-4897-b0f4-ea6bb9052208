spring.jpa.hibernate.ddl-auto=create
spring.jpa.generate-ddl=true
spring.datasource.url=jdbc:h2:mem:myDb;DB_CLOSE_DELAY=-1;NON_KEYWORDS=KEY,VALUE
spring.datasource.driver-class-name=org.h2.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
javax.persistence.schema-generation.scripts.action=create

hibernate.dialect=
hibernate.hbm2ddl.auto=create


#spring.jpa.show-sql: true
server.address=127.0.0.1
server.port=8881
server.compression.enabled=true
server.http2.enabled=true