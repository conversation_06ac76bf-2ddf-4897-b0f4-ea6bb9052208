version: '3'
services:
  agent:
    image: "datadog/agent:7.21.1"
    environment:
      - DD_API_KEY
      - DD_APM_ENABLED=true
      - DD_LOGS_ENABLED=true
      - DD_LOGS_CONFIG_CONTAINER_COLLECT_ALL=true
      - DD_PROCESS_AGENT_ENABLED=true
      - DD_DOCKER_LABELS_AS_TAGS={"my.custom.label.team":"team"}
      - DD_DOCKER_LABELS_AS_TAGS={"my.custom.label.stage":"stage"}
      - DD_TAGS=__TAGS__
      - DD_HOSTNAME=__HOSTNAME_BASE__
    ports:
      - "8126:8126"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /proc/:/host/proc/:ro
      - /sys/fs/cgroup/:/host/sys/fs/cgroup:ro
    labels:
      com.datadoghq.ad.logs: '[{"source": "agent", "service": "agent"}]'
  discounts:
    environment:
      - FLASK_APP=discounts.py
      - FLASK_DEBUG=1
      - POSTGRES_PASSWORD=__PG_USER__
      - POSTGRES_USER=__PG_PASS__
      - POSTGRES_HOST=db
      - DD_SERVICE=discounts-service
      - DD_AGENT_HOST=agent
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DD_PROFILING_ENABLED=true
    build: ../../discounts-service/
    command: ddtrace-run flask run --port=5001 --host=0.0.0.0
    volumes:
      - "../../discounts-service:/app"
    ports:
      - "5001:5001"
    depends_on:
      - agent
      - db
    labels:
      # com.datadoghq.ad.logs: '[{"source": "python", "service": "discounts-service"}]'
      com.datadoghq.ad.logs: '[{"source": "python", "service": "discounts-service", "log_processing_rules": [{"type": "multi_line", "name": "new_log_start_with_date", "pattern": "\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])"}]}]'
      my.custom.label.team: "discount"
      my.custom.label.stage: "working"
  frontend:
    environment:
      - DD_AGENT_HOST=agent
      - DD_SERVICE=store-frontend
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DB_USERNAME=__PG_USER__
      - DB_PASSWORD=__PG_PASS__
      - DD_CLIENT_TOKEN
      - DD_APPLICATION_ID
    # image: "ddtraining/ecommerce-frontend:latest"
    build: ../../store-frontend-instrumented-fixed/
    command: sh docker-entrypoint.sh
    ports:
      - "8000:3000"
    depends_on:
      - agent
      - db
      - discounts
      - advertisements
    labels:
      com.datadoghq.ad.logs: '[{"source": "ruby", "service": "store-frontend"}]'
      my.custom.label.team: "frontend"
      my.custom.label.stage: "working"
  advertisements:
    environment:
      - FLASK_APP=ads.py
      - FLASK_DEBUG=1
      - POSTGRES_PASSWORD=__PG_USER__
      - POSTGRES_USER=__PG_PASS__
      - DD_SERVICE=advertisements-service
      - DD_AGENT_HOST=agent
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DD_PROFILING_ENABLED=true
    image: "ddtraining/advertisements-service:latest"
    command: ddtrace-run flask run --port=5002 --host=0.0.0.0
    ports:
      - "5002:5002"
    depends_on:
      - agent
      - db
    labels:
      com.datadoghq.ad.logs: '[{"source": "python", "service": "ads-service"}]'
      my.custom.label.team: "advertisements"
      my.custom.label.stage: "working"
  db:
    image: postgres:11-alpine
    restart: always
    environment:
      - POSTGRES_PASSWORD=__PG_USER__
      - POSTGRES_USER=__PG_PASS__
    labels:
      com.datadoghq.ad.logs: '[{"source": "postgresql", "service": "postgres"}]'
      my.custom.label.stage: "working"

  discounts-broken:
    environment:
      - FLASK_APP=discounts.py
      - FLASK_DEBUG=1
      - DD_SERVICE=discounts-service
      - DD_AGENT_HOST=agent
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DD_PROFILING_ENABLED=true
      - DD_VERSION=1.0
    image: "ddtraining/discounts-service:latest"
    command: ddtrace-run flask run --port=5011 --host=0.0.0.0
    ports:
      - "5011:5011"
    volumes:
      - "../../discounts-service:/app"
    depends_on:
      - agent
      - db-broken
    labels:
      # com.datadoghq.ad.logs: '[{"source": "python", "service": "discounts-service"}]'
      com.datadoghq.ad.logs: '[{"source": "python", "service": "discounts-service", "log_processing_rules": [{"type": "multi_line", "name": "new_log_start_with_date", "pattern": "\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])"}]}]'
      my.custom.label.stage: "broken"
  frontend-broken:
    environment:
      - DD_AGENT_HOST=agent
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DD_SERVICE=store-frontend
      - DD_VERSION=1.0
      - DB_USERNAME=__PG_USER__
      - DB_PASSWORD=__PG_PASS__
      - DD_CLIENT_TOKEN
      - DD_APPLICATION_ID
    image: "ddtraining/ecommerce-frontend:latest"
    build: ../../store-frontend-broken-instrumented/
    command: sh docker-entrypoint.sh
    ports:
      - "8080:3000"
    depends_on:
      - agent
      - db-broken
      - discounts-broken
      - advertisements-broken
    labels:
      com.datadoghq.ad.logs: '[{"source": "ruby", "service": "store-frontend"}]'
      my.custom.label.stage: "broken"
  advertisements-broken:
    environment:
      - FLASK_APP=ads.py
      - FLASK_DEBUG=1
      - POSTGRES_PASSWORD=__PG_USER__
      - POSTGRES_USER=__PG_PASS__
      - DD_SERVICE=advertisements-service
      - DD_AGENT_HOST=agent
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DD_PROFILING_ENABLED=true
      - DD_VERSION=1.0
    image: "ddtraining/advertisements-service:latest"
    command: ddtrace-run flask run --port=5012 --host=0.0.0.0
    ports:
      - "5012:5012"
    volumes:
      - "../../ads-service:/app"
    depends_on:
      - agent
      - db-broken
    labels:
      com.datadoghq.ad.logs: '[{"source": "python", "service": "ads-service"}]'
      my.custom.label.stage: "broken"
  db-broken:
    image: postgres:11-alpine
    restart: always
    environment:
      - POSTGRES_PASSWORD=__PG_USER__
      - POSTGRES_USER=__PG_PASS__
    labels:
      com.datadoghq.ad.logs: '[{"source": "postgresql", "service": "postgres"}]'
      my.custom.label.stage: "broken"
