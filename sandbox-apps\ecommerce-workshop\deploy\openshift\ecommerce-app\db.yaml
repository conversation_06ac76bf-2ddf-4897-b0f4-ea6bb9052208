kind: ServiceAccount
apiVersion: v1
metadata:
  name: postgres
---
apiVersion: v1
kind: Secret
metadata:
  name: db-password
  labels:
    app: ecommerce
    service: db
type: Opaque
data:
  pw: password
---
  apiVersion: v1
  kind: PersistentVolume
  metadata:
    name: task-pv-volume 
    labels:
      type: local
  spec:
    storageClassName: manual 
    capacity:
      storage: 5Gi
    accessModes:
      - ReadWriteOnce 
    persistentVolumeReclaimPolicy: Retain
    hostPath:
      path: "/mnt/data" 
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: task-pvc-volume
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  storageClassName: manual
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    service: db
    app: ecommerce
  name: db
spec:
  replicas: 1
  selector:
    matchLabels:
      service: db
      app: ecommerce
  strategy: {}
  template:
    metadata:
      labels:
        service: db
        app: ecommerce
    spec:
      containers:
      - image: postgres:11-alpine
        name: postgres
        securityContext:
          privileged: true 
        ports:
          - containerPort: 5432
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              key: pw
              name: db-password
        - name: POSTGRES_USER
          value: "user"
        - name: PGDATA
          value: "/var/lib/postgresql/data/mydata"
        resources: {}
        volumeMounts:
        - mountPath: /var/lib/postgresql/data
          name: postgresdb 
      serviceAccountName: postgres
      volumes:
      - name: postgresdb
        persistentVolumeClaim:
            claimName: task-pvc-volume
---
apiVersion: v1
kind: Service
metadata:
  creationTimestamp: null
  labels:
    app: ecommerce
    service: db 
  name: db
spec:
  ports:
  - port: 5432
    protocol: TCP
    targetPort: 5432
  selector:
    app: ecommerce
    service: db
status:
  loadBalancer: {}