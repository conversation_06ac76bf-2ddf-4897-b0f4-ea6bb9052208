# Google AI Startup School - Observing LLM Applications with Datadog <img src="./resources/dd_icon.png" alt="Datadog Logo" width="50"/>
Thank you for attending our session on LLM Observability with Datadog! This page contains all the resources and links we discussed, plus additional materials to help you implement LLM monitoring in your applications.

## Session Recap
In this session, we explored how to observe, troubleshoot, and optimize LLM-based applications using Datadog's LLM Observability features, built to support production workloads with Google Vertex AI's Gemini models. Through our live demo of Swagbot, we demonstrated how Datadog surfaces key insights across performance, reliability, and security.

## Get Started with LLM Observability
Visit [this page](https://www.datadoghq.com/product/llm-observability/) to start a **free trial** of LLM Observability and explore the full capabilities of the Datadog platform!

## Preview Links
### Dataset & Experiments
Visit [this](https://docs.datadoghq.com/llm_observability/experiments_preview) page.
### Agentic Workflow Monitoring
Visit [this](https://docs.datadoghq.com/llm_observability/agent_monitoring) page.

## Implementation Guide
### 1. Core Documentation
- [LLM Observability Overview](https://docs.datadoghq.com/llm_observability)
- [AI Agentic Monitoring](https://docs.datadoghq.com/llm_observability/agent_monitoring)
- [Datasets & Experiments](https://docs.datadoghq.com/llm_observability/experiments_preview)

### 2. Getting Started with LLM Observability
- [LLM Observability Quickstart Guide](https://docs.datadoghq.com/llm_observability/quickstart/?tab=python)
- [Tracing LLM Applications Guide](https://docs.datadoghq.com/llm_observability/setup/?tab=decorators)
- [LLM Chain Jupyter notebooks](https://github.com/DataDog/llm-observability/tree/main) - These notebooks introduce you to Datadog's LLM Observability Python SDK using hands-on examples.

### 3. Example Implementation
- [Swagbot Source Code](https://github.com/DataDog/dpn/tree/master/sandbox-apps/swagbot) - The chatbot application demonstrated during the workshop

## Learning Resources
### Featured Articles
- [Monitor your Google Gemini apps with Datadog LLM Observability](https://www.datadoghq.com/blog/monitor-google-gemini-datadog-llm-observability/)
- [Datadog's broader Google Cloud Monitoring capabilities](https://www.datadoghq.com/solutions/googlecloud/)
- [Monitor, troubleshoot, and improve agentic systems with Datadog](https://www.datadoghq.com/blog/monitor-ai-agents/)
- [Create and monitor LLM experiments with Datadog](https://www.datadoghq.com/blog/llm-experiments/)
- [Detect hallucinations in your RAG LLM applications with Datadog LLM Observability](https://www.datadoghq.com/blog/llm-observability-hallucination-detection/)

### Self-Paced Learning
- [Datadog Learning Platform](https://learn.datadoghq.com/pages/learning-paths) - Self paced & hands On learning courses

## Datadog for Startups - [Sign up here](https://www.datadoghq.com/partner/datadog-for-startups/)
For Series A startups who are not existing Datadog customers and looking to scale with Datadog:
- Access to the full platform (up to $100K in credits)
- Personalized support and guidance
- Access to Datadog's technical resources
- Ongoing customer success support

## Get Involved
- [Datadog Partner Program](https://partners.datadoghq.com/s/login/?ec=302&startURL=%2Fs%2F) - Join our partner ecosystem!
- [Datadog Events](https://www.datadoghq.com/events/) - Stay updated on upcoming webinars and conferences

---
