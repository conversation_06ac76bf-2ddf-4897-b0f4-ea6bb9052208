{{- if .Values.loadGenerator.create }}
{{- if .Values.serviceAccounts.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.loadGenerator.name }}
  namespace: {{.Release.Namespace}}
  {{- if not .Values.serviceAccounts.annotationsOnlyForCartservice }}
  {{- with .Values.serviceAccounts.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
---
{{- end }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.loadGenerator.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.loadGenerator.name }}
  replicas: 1
  template:
    metadata:
      labels:
        app: {{ .Values.loadGenerator.name }}
      annotations:
        sidecar.istio.io/rewriteAppHTTPProbers: "true"
    spec:
      {{- if .Values.serviceAccounts.create }}
      serviceAccountName: {{ .Values.loadGenerator.name }}
      {{- else }}
      serviceAccountName: default
      {{- end }}
      terminationGracePeriodSeconds: 5
      restartPolicy: Always
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 1000
        {{- if .Values.seccompProfile.enable }}
        seccompProfile:
          type: {{ .Values.seccompProfile.type }}
        {{- end }}
      {{- if .Values.loadGenerator.checkFrontendInitContainer }}
      initContainers:
      - command:
        - /bin/sh
        - -exc
        - |
          echo "Init container pinging frontend: ${FRONTEND_ADDR}..."
          STATUSCODE=$(wget --server-response http://${FRONTEND_ADDR} 2>&1 | awk '/^  HTTP/{print $2}')
          if test $STATUSCODE -ne 200; then
              echo "Error: Could not reach frontend - Status code: ${STATUSCODE}"
              exit 1
          fi
        name: frontend-check
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - all
          privileged: false
          readOnlyRootFilesystem: true
        image: busybox:latest
        env:
        - name: FRONTEND_ADDR
          value: "{{ .Values.frontend.name }}:80"
      {{- end }}
      containers:
      - name: main
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - all
          privileged: false
          readOnlyRootFilesystem: true
        image: {{ .Values.images.repository }}/{{ .Values.loadGenerator.name }}:{{ .Values.images.tag | default .Chart.AppVersion }}
        env:
        - name: FRONTEND_ADDR
          value: "{{ .Values.frontend.name }}:80"
        - name: USERS
          value: "10"
        resources:
          requests:
            cpu: 300m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
{{- if .Values.networkPolicies.create }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ .Values.loadGenerator.name }}
  namespace: {{ .Release.Namespace }}
spec:
  podSelector:
    matchLabels:
      app: {{ .Values.loadGenerator.name }}
  policyTypes:
  - Egress
  egress:
  - {}
{{- end }}
{{- if .Values.sidecars.create }}
---
apiVersion: networking.istio.io/v1beta1
kind: Sidecar
metadata:
  name: {{ .Values.loadGenerator.name }}
  namespace: {{ .Release.Namespace }}
spec:
  workloadSelector:
    labels:
      app: {{ .Values.loadGenerator.name }}
  egress:
  - hosts:
    - istio-system/*
    - ./{{ .Values.frontend.name }}.{{ .Release.Namespace }}.svc.cluster.local
    {{- if .Values.opentelemetryCollector.create }}
    - ./{{ .Values.opentelemetryCollector.name }}.{{ .Release.Namespace }}.svc.cluster.local
    {{- end }}
{{- end }}
{{- end }}
