name: Release Changelog

# Only release on a new tag that is a version number.
on:
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+'

jobs:
  release_a_changelog:
    name: Release a Changelog
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Release a Changelog
        uses: rasmus-saks/release-a-changelog-action@v1.0.1
        with:
          github-token: '${{ secrets.GH_TOKEN }}'

  publish_service_containers:
    name: Publish service containers
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Login to ECR
        id: login-ecr
        uses: docker/login-action@v1
        with:
           registry: public.ecr.aws
           username: ${{ secrets.AWS_ACCESS_KEY_ID }}
           password: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Download, Tag, and Push Service Images
        run: |
          TAG=${GITHUB_REF/refs\/tags\//}

          IMAGES=(
            ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/advertisements
            ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/advertisements-fixed
            ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/advertisements-errors
            ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/discounts
            ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/discounts-fixed
            ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/discounts-react-app
            ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/storefront
            ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/storefront-fixed
            ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/storefront-no-instrumentation
            ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/attackbox
            ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/nginx
          )

          for i in "${IMAGES[@]}"
          do
            echo "$i"
            docker pull "$i":latest
            docker tag "$i":latest "$i":$TAG
            docker push "$i":$TAG
          done

          echo "All done"

