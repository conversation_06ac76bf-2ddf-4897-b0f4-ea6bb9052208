
# Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.
# This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2020 Datadog, Inc.


# Replace all REPLACE_ME strings with your own values


#################
#               #
# Required vars #
#               #
#################

# Datadog keys
DD_API_KEY=REPLACE_ME
DD_APP_KEY=REPLACE_ME

# postgres username and password
PG_USER=REPLACE_ME
PG_PASS=REPLACE_ME

HOSTNAME_BASE=REPLACE_ME
# HOSTNAME_BASE will become part of the hostname for your sandbox environment.
# it will help clarify what host is which in your Datadog account.
# example: HOSTNAME_BASE=jane.doe.laptop

TAG_DEFAULTS="REPLACE_ME REPLACE_ME"
# TAG_DEFAULTS defines what host tags are applied to the sandbox environment by default
# example: TAG_DEFAULTS="creator:jane.doe role:sandbox laptop:oct-2019"
