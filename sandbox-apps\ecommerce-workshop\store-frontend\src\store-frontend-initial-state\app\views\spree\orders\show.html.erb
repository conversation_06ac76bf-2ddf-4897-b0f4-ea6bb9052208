<fieldset id="order_summary" data-hook>
  <h1><%= accurate_title %></h1>

  <% if order_just_completed?(@order) %>
    <p><strong><%= Spree.t(:thank_you_for_your_order) %></strong></p>
  <% end %>

  <div id="order" data-hook>
    <%= render partial: 'spree/shared/order_details', locals: { order: @order } %>

    <br class="clear" />

    <p data-hook="links">
      <%= link_to Spree.t(:back_to_store), spree.root_path, class: "button" %>
      <% unless order_just_completed?(@order) %>
        <% if try_spree_current_user && respond_to?(:account_path) %>
          <%= link_to Spree.t(:my_account), account_path, class: "button" %>
        <% end %>
      <% end %>
    </p>
  </div>
</fieldset>
