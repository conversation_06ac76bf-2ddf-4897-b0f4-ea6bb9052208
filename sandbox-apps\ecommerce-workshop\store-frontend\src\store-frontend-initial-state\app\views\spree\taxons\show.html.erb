<h1 class="taxon-title"><%= @taxon.name %></h1>

<% content_for :sidebar do %>
  <div data-hook="taxon_sidebar_navigation">
    <%= render partial: 'spree/shared/taxonomies' %>
    <%= render partial: 'spree/shared/filters' if @taxon.leaf? %>
  </div>
<% end %>

<div data-hook="taxon_products">
  <%= render partial: 'spree/shared/products', locals: { products: @products, taxon: @taxon } %>
</div>

<% unless params[:keywords].present? %>
  <div data-hook="taxon_children">
    <% cache [I18n.locale, @taxon] do %>
      <%= render partial: 'taxon', collection: @taxon.children %>
    <% end %>
  </div>
<% end %>
