{{- if .Values.cartService.create }}
{{- if .Values.serviceAccounts.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.cartService.name }}
  namespace: {{.Release.Namespace}}
  {{- with .Values.serviceAccounts.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
---
{{- end }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.cartService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.cartService.name }}
  template:
    metadata:
      {{- if .Values.cartDatabase.externalRedisTlsOrigination.enable }}
      annotations:
        sidecar.istio.io/userVolumeMount: '[{"name": "{{ .Values.cartDatabase.externalRedisTlsOrigination.name }}", "mountPath": "/etc/certs", "readonly": true}]'
        sidecar.istio.io/userVolume: '[{"name": "{{ .Values.cartDatabase.externalRedisTlsOrigination.name }}", "secret": {"secretName": "{{ .Values.cartDatabase.externalRedisTlsOrigination.name }}"}}]'
        proxy.istio.io/config: '{"holdApplicationUntilProxyStarts": true}'
      {{- end }}
      labels:
        app: {{ .Values.cartService.name }}
    spec:
      {{- if .Values.serviceAccounts.create }}
      serviceAccountName: {{ .Values.cartService.name }}
      {{- else }}
      serviceAccountName: default
      {{- end }}
      terminationGracePeriodSeconds: 5
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 1000
        {{- if .Values.seccompProfile.enable }}
        seccompProfile:
          type: {{ .Values.seccompProfile.type }}
        {{- end }}
      containers:
      - name: server
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - all
          privileged: false
          readOnlyRootFilesystem: true
        {{- if .Values.nativeGrpcHealthCheck }}
        image: {{ .Values.images.repository }}/{{ .Values.cartService.name }}:{{ .Values.images.tag | default .Chart.AppVersion }}-native-grpc-probes
        {{- else }}
        image: {{ .Values.images.repository }}/{{ .Values.cartService.name }}:{{ .Values.images.tag | default .Chart.AppVersion }}
        {{- end }}
        ports:
        - containerPort: 7070
        env:
        {{- if eq .Values.cartDatabase.type "spanner" }}
        - name: SPANNER_CONNECTION_STRING
        {{- else }}
        - name: REDIS_ADDR
        {{- end }}
          value: {{ .Values.cartDatabase.connectionString | quote }}
        resources:
          requests:
            cpu: 200m
            memory: 64Mi
          limits:
            cpu: 300m
            memory: 128Mi
        readinessProbe:
          initialDelaySeconds: 15
          {{- if .Values.nativeGrpcHealthCheck }}
          grpc:
            port: 7070
          {{- else }}
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:7070", "-rpc-timeout=5s"]
          {{- end }}
        livenessProbe:
          initialDelaySeconds: 15
          periodSeconds: 10
          {{- if .Values.nativeGrpcHealthCheck }}
          grpc:
            port: 7070
          {{- else }}
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:7070", "-rpc-timeout=5s"]
          {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.cartService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  type: ClusterIP
  selector:
    app: {{ .Values.cartService.name }}
  ports:
  - name: grpc
    port: 7070
    targetPort: 7070
{{- if .Values.networkPolicies.create }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ .Values.cartService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  podSelector:
    matchLabels:
      app: {{ .Values.cartService.name }}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: {{ .Values.frontend.name }}
    - podSelector:
        matchLabels:
          app: {{ .Values.checkoutService.name }}
    ports:
     - port: 7070
       protocol: TCP
  egress:
  - {}
{{- end }}
{{- if .Values.sidecars.create }}
---
apiVersion: networking.istio.io/v1beta1
kind: Sidecar
metadata:
  name: {{ .Values.cartService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  workloadSelector:
    labels:
      app: {{ .Values.cartService.name }}
  egress:
  - hosts:
    - istio-system/*
    {{- if eq .Values.cartDatabase.type "redis" }}
    {{- if .Values.cartDatabase.externalRedisTlsOrigination.enable }}
    - ./{{ .Values.cartDatabase.externalRedisTlsOrigination.name }}.{{ .Release.Namespace }}
    {{- else }}
    - ./{{ .Values.cartDatabase.inClusterRedis.name }}.{{ .Release.Namespace }}.svc.cluster.local
    {{- end }}
    {{- end }}
    {{- if .Values.opentelemetryCollector.create }}
    - ./{{ .Values.opentelemetryCollector.name }}.{{ .Release.Namespace }}.svc.cluster.local
    {{- end }}
{{- end }}
{{- if .Values.authorizationPolicies.create }}
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ .Values.cartService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.cartService.name }}
  rules:
  - from:
    - source:
        principals:
        {{- if .Values.serviceAccounts.create }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.frontend.name }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.checkoutService.name }}
        {{- else }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/default
        {{- end }}
    to:
    - operation:
        paths:
        - /hipstershop.CartService/AddItem
        - /hipstershop.CartService/GetCart
        - /hipstershop.CartService/EmptyCart
        methods:
        - POST
        ports:
        - "7070"
{{- end }}
{{- end }}
