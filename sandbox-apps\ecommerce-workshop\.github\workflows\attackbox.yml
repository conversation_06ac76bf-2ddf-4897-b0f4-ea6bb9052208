name: Attackbox Test and Build

on:
  push:
    branches: [ main ]
    paths:
      - attackbox/**
  workflow_dispatch:
    branches: [ main ]

defaults:
  run:
    working-directory: attackbox

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v1

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v1

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Login to ECR
      id: login-ecr
      uses: docker/login-action@v1
      with:
        registry: public.ecr.aws
        username: ${{ secrets.AWS_ACCESS_KEY_ID }}
        password: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

    - name: Build and push
      uses: docker/build-push-action@v2
      with:
        context: ./attackbox
        platforms: linux/amd64
        push: true
        tags: ${{ secrets.PUBLIC_ECR_REGISTRY }}/ddtraining/attackbox:latest

