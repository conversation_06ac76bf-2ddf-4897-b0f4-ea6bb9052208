<%# Link showing page number
  - available local variables
    page:          a page object for "this" page
    url:           url to this page
    current_page:  a page object for the currently displayed page
    total_pages:   total number of pages
    per_page:      number of items to fetch per page
    remote:        data-remote
-%>
<li class="page<%= ' active' if page.current? %> page-item">
  <%= link_to page,
    url,
    opts = {
      remote: remote,
      rel: page.next? ? 'next' : page.prev? ? 'prev' : nil,
      class: 'page-link'
    } %>
</li>
