<% content_for :head do %>
  <% if products.respond_to?(:total_pages) %>
    <%= rel_next_prev_link_tags products %>
  <% end %>
<% end %>

<div data-hook="products_search_results_heading">
  <% if products.empty? %>
    <div data-hook="products_search_results_heading_no_results_found">
      <%= Spree.t(:no_products_found) %>
    </div>
  <% elsif params.key?(:keywords) %>
    <div data-hook="products_search_results_heading_results_found">
      <h6 class="search-results-title"><%= Spree.t(:search_results, keywords: h(params[:keywords])) %></h6>
    </div>
  <% end %>
</div>

<% if products.any? %>
<% render :template => "discounts/get" %>
  <div id="products" class="row d-flex" data-hook>
    <%= render partial: 'spree/products/product', collection: products, locals: { taxon: @taxon } %>
  </div>
<% end %>

<% if products.respond_to?(:total_pages) %>
  <%= paginate products, theme: 'twitter-bootstrap-4' %>
<% end %>
