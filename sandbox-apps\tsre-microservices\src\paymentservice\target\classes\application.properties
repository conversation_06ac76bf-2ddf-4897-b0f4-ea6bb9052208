spring.jpa.hibernate.ddl-auto=update
spring.jpa.generate-ddl=true
spring.datasource.url=***************************************
spring.datasource.username=swagstore
spring.datasource.password=weLoveSwagAtDash2023
spring.datasource.driver-class-name=org.mariadb.jdbc.Driver
# Test settings
#spring.datasource.url=jdbc:h2:mem:paymentdb;DB_CLOSE_DELAY=-1;NON_KEYWORDS=KEY,VALUE
#spring.datasource.driver-class-name=org.h2.Driver
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
#spring.datasource.username=sa
#spring.datasource.password=password
#spring.jpa.database-platform=org.hibernate.dialect.H2Dialect


javax.persistence.schema-generation.scripts.action=create
#spring.jpa.show-sql: true
server.address=0.0.0.0
server.port=8881
server.compression.enabled=true
server.http2.enabled=true