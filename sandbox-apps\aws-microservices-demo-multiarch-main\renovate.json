{"extends": ["config:base"], "pip-compile": {"enabled": true, "fileMatch": ["(^|/)requirements\\.in$"], "lockFileMaintenance": {"enabled": true}}, "constraints": {"python": "~=3.10.0"}, "prConcurrentLimit": 8, "stabilityDays": 7, "vulnerabilityAlerts": {"labels": ["type:security"], "stabilityDays": 0}, "postUpdateOptions": ["gomodTidy", "npmDedupe"], "labels": ["dependencies"], "python": {"addLabels": ["lang: python"]}, "java": {"addLabels": ["lang: java"]}, "golang": {"addLabels": ["lang: go"]}, "nuget": {"addLabels": ["lang: dotnet"]}, "npm": {"addLabels": ["lang: nodejs"]}, "docker": {"pinDigests": true}, "kubernetes": {"fileMatch": ["\\.yaml$"], "ignorePaths": ["release/**", "kustomize/base/**", "kubernetes-manifests/**"]}, "regexManagers": [{"fileMatch": ["(^|/)Dockerfile[^/]*$"], "matchStrings": ["# renovate: datasource=(?<datasource>[a-z-]+?) depName=(?<depName>[^\\s]+?)(?: (lookupName|packageName)=(?<packageName>[^\\s]+?))?(?: versioning=(?<versioning>[a-z-0-9]+?))?\\s(?:ENV|ARG) .+?_VERSION[ =]\"?(?<currentValue>.+?)\"?\\s"]}], "packageRules": [{"matchUpdateTypes": ["minor", "patch", "digest"], "automerge": true}], "platformAutomerge": true}