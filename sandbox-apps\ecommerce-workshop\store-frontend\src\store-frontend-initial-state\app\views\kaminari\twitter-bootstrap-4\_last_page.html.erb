<%# Link to the "Last" page
  - available local variables
    url:           url to the last page
    current_page:  a page object for the currently displayed page
    total_pages:   total number of pages
    per_page:      number of items to fetch per page
    remote:        data-remote
-%>
<% unless current_page.last? %>
  <li class="last next page-item"><%# "next" class present for border styling in twitter bootstrap %>
    <%= link_to_unless current_page.last?, raw(t 'views.pagination.last'), url, remote: remote, class: 'page-link' %>
  </li>
<% end %>
