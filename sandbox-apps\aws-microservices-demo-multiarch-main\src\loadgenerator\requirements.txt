#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile --output-file=requirements.txt requirements.in
#
brotli==1.0.9
    # via geventhttpclient
certifi==2022.12.7
    # via
    #   geventhttpclient
    #   requests
charset-normalizer==2.1.1
    # via requests
click==8.1.3
    # via flask
configargparse==1.5.3
    # via locust
flask==2.2.2
    # via
    #   flask-basicauth
    #   flask-cors
    #   locust
flask-basicauth==0.2.0
    # via locust
flask-cors==3.0.10
    # via locust
gevent==22.10.2
    # via
    #   geventhttpclient
    #   locust
geventhttpclient==2.0.8
    # via locust
greenlet==2.0.1
    # via gevent
idna==3.4
    # via requests
importlib-metadata==5.1.0
    # via flask
itsdangerous==2.1.2
    # via flask
jinja2==3.1.2
    # via flask
locust==2.14.0
    # via -r requirements.in
markupsafe==2.1.1
    # via
    #   jinja2
    #   werkzeug
msgpack==1.0.4
    # via locust
psutil==5.9.4
    # via locust
pyzmq==24.0.1
    # via locust
requests==2.28.1
    # via locust
roundrobin==0.0.4
    # via locust
six==1.16.0
    # via
    #   flask-cors
    #   geventhttpclient
typing-extensions==4.4.0
    # via locust
urllib3==1.26.13
    # via requests
werkzeug==2.2.2
    # via
    #   flask
    #   locust
zipp==3.11.0
    # via importlib-metadata
zope-event==4.6
    # via gevent
zope-interface==5.5.2
    # via gevent

# The following packages are considered to be unsafe in a requirements file:
# setuptools
