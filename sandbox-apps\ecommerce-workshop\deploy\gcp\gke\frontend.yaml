apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    service: frontend
    app: ecommerce
  name: frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      service: frontend
      app: ecommerce
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        service: frontend
        app: ecommerce
    spec:
      containers:
      - args:
        - docker-entrypoint.sh
        command:
        - sh
        env:
        - name: DB_USERNAME
          value: user
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: pw
              name: db-password
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP
        - name: DD_LOGS_INJECTION
          value: "true"
        - name: DD_ANALYTICS_ENABLED
          value: "true"
        - name: DD_ENV
          value: "development"
        - name: DD_SITE
          value: "datadoghq.com"
        - name: DD_SERVICE
          value: "store-frontend"
        # Enable RUM
        #- name: DD_CLIENT_TOKEN
        #  value: ""
        #- name: DD_APPLICATION_ID
        #  value: ""
        image: ddtraining/storefront-fixed:2.1.2
        imagePullPolicy: Always
        name: ecommerce-spree-observability
        ports:
        - containerPort: 3000
          protocol: TCP
        resources:
          requests:
            cpu: 100m
            memory: 100Mi
          limits: {}
---
apiVersion: v1
kind: Service
metadata:
  labels:
    service: frontend
    app: ecommerce
  name: frontend
spec:
  ports:
  - port: 3000
    protocol: TCP
    targetPort: 3000
  selector:
    service: frontend
    app: ecommerce
  type: LoadBalancer
---
apiVersion: networking.k8s.io/v1beta1
kind: Ingress
metadata:
  name: basic-ingress
spec:
  backend:
    serviceName: frontend
    servicePort: 3000
