# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# [START cloudbuild_microservice_demo_cloudbuild]

# This configuration file is used to build and deploy the app into a
# GKE cluster using Google Cloud Build.
#
# PREREQUISITES:
# - Cloud Build service account must have role: "Kubernetes Engine Developer"

# USAGE:
# GCP zone and GKE target cluster must be specified as substitutions
# Example invocation:
# `gcloud builds submit --config=cloudbuild.yaml --substitutions=_ZONE=us-central1-b,_CLUSTER=demo-app-staging .`

steps:
- id: 'Deploy application to cluster'
  name: 'gcr.io/k8s-skaffold/skaffold:v2.0.3'
  entrypoint: 'bash'
  args:
  - '-c'
  - >
    gcloud container clusters get-credentials --zone=$_ZONE $_CLUSTER;
    skaffold run -f=skaffold.yaml --default-repo=gcr.io/$PROJECT_ID;

# Add more power, and more time, for heavy Skaffold build
timeout: '3600s'
options:
  machineType: 'N1_HIGHCPU_8'
  
# [END cloudbuild_microservice_demo_cloudbuild]