# How to Contribute

Thank you so much for your interest in contributing to Online Boutique.
Before contributing, you must:
* Sign the [Contributor License Agreement (CLA)](#contributor-license-agreement).
* Follow the [Google Open Source Community Guidelines](https://opensource.google.com/conduct/).
* Follow the [Contribution Process](#contribution-process).

## Contributor License Agreement

Contributions to Online Boutique must be accompanied by a Contributor License
Agreement (CLA). You (or your employer) retain the copyright to your contribution.
The CLA gives us permission to use and redistribute your contributions as
part of the project. Head over to <https://cla.developers.google.com/> to see
your current agreements on file or to sign a new one.

You generally only need to submit a CLA once, so if you've already submitted one
(even if it was for a different project), you probably don't need to do it
again.

## Contribution Process

Here's the process for making a change to this repository:

1. Review Online Boutique's [purpose](/docs/purpose.md) and [product requirements](/docs/product-requirements.md).
1. If your proposed changes **do not align** with the purpose and product requirements of Online Boutique, you may be asked to instead maintain your own fork of this repository.
1. For **small changes** (such as a bug fixes or spelling corrections):
    1. Fork this repository and submit a [pull request](https://help.github.com/articles/about-pull-requests/).
    1. Wait for a maintainer of this repository to review your change.
1. For **bigger changes**:
    1. Create a [GitHub issue](https://github.com/GoogleCloudPlatform/microservices-demo/issues/new/choose) describing the change **before** working on the implementation. This is important to avoid potentially having to discard your development efforts.
    1. Wait for a maintainer of this repository to review your GitHub issue. For significantly complex proposals, you may be asked to start a Google Doc to discuss design decisions.

If you have any questions, please [create a GitHub issue](https://github.com/GoogleCloudPlatform/microservices-demo/issues/new/choose).
