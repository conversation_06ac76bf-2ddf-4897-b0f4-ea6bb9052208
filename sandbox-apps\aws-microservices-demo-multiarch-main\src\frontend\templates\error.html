<!--
 Copyright 2020 Google LLC

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

{{ define "error" }}
    {{ template "header" . }}
    <div {{ with $.platform_css }} class="{{.}}" {{ end }}>
        <span class="platform-flag">
          {{$.platform_name}}
        </span>
      </div>
    <main role="main">
        <div class="py-5">
            <div class="container bg-light py-3 px-lg-5 py-lg-5">
                <h1>Uh, oh!</h1>
                <p>Something has failed. Below are some details for debugging.</p>

                <p><strong>HTTP Status:</strong> {{.status_code}} {{.status}}</p>
                <pre class="border border-danger p-3"
                    style="white-space: pre-wrap; word-break: keep-all;">
                    {{- .error -}}
                </pre>
            </div>
        </div>
    </main>

    {{ template "footer" . }}
    {{ end }}