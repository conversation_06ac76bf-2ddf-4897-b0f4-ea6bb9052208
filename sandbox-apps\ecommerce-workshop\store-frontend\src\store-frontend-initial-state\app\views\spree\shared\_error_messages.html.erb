<% if target && target.errors.any? %>
  <div id="errorExplanation" class="alert alert-danger" data-hook>
    <h3><%= Spree.t(:errors_prohibited_this_record_from_being_saved, count: target.errors.count) %>:</h3>
    <p><%= Spree.t(:there_were_problems_with_the_following_fields) %>:</p>
     <ul>
       <% target.errors.full_messages.each do |msg| %>
         <li><%= msg %></li>
       <% end %>
     </ul>
     <br/>
  </div>
<% end %>
