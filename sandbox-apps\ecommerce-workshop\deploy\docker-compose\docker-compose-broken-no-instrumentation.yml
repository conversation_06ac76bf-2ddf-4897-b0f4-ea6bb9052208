version: '3'
services:
  discounts:
    environment:
      - FLASK_APP=discounts.py
      - FLASK_DEBUG=1
      - POSTGRES_PASSWORD
      - POSTGRES_USER
      - POSTGRES_HOST=db
    image: "ddtraining/discounts:2.1.0"
    command: flask run --port=5001 --host=0.0.0.0
    ports:
      - "5001:5001"
    depends_on:
      - db
  frontend:
    environment:
      - DB_USERNAME
      - DB_PASSWORD
    image: "ddtraining/storefront:2.1.0"
    ports:
      - "3000:3000"
    depends_on:
      - db
      - discounts
      - advertisements
  advertisements:
    environment:
      - FLASK_APP=ads.py
      - FLASK_DEBUG=1
      - POSTGRES_PASSWORD
      - POSTGRES_USER
      - POSTGRES_HOST=db
    image: "ddtraining/advertisements:2.1.0"
    command: flask run --port=5002 --host=0.0.0.0
    ports:
      - "5002:5002"
    depends_on:
      - db
  db:
    image: postgres:11-alpine
    restart: always
    environment:
      - POSTGRES_PASSWORD
      - POSTGRES_USER
