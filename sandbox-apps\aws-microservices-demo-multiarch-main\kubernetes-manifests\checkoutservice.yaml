# Copyright 2018 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

apiVersion: apps/v1
kind: Deployment
metadata:
  name: checkoutservice
  labels:
      tags.datadoghq.com/env: "dev-auto-inject"
      tags.datadoghq.com/service: "checkoutservice"
      tags.datadoghq.com/version: "0.5.0"
spec:
  selector:
    matchLabels:
      app: checkoutservice
  template:
    metadata:
      labels:
        app: checkoutservice
        tags.datadoghq.com/env: "dev-auto-inject"
        tags.datadoghq.com/service: "checkoutservice"
        tags.datadoghq.com/version: "0.5.0"
        admission.datadoghq.com/enabled: "true"
    spec:
      serviceAccountName: default
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 1000
      containers:
        - name: server
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            # readOnlyRootFilesystem: true
          image: checkoutservice
          ports:
          - containerPort: 5050
          # readinessProbe:
          #   exec:
          #     command: ["/bin/grpc_health_probe", "-addr=:5050"]
          # livenessProbe:
          #   exec:
          #     command: ["/bin/grpc_health_probe", "-addr=:5050"]
          env:
          - name: PORT
            value: "5050"
          - name: PRODUCT_CATALOG_SERVICE_ADDR
            value: "productcatalogservice:3550"
          - name: SHIPPING_SERVICE_ADDR
            value: "shippingservice:50051"
          - name: PAYMENT_SERVICE_ADDR
            value: "paymentservice:50051"
          - name: EMAIL_SERVICE_ADDR
            value: "emailservice:5000"
          - name: CURRENCY_SERVICE_ADDR
            value: "currencyservice:7000"
          - name: CART_SERVICE_ADDR
            value: "cartservice:7070"
          - name: DISABLE_PROFILER
            value: "1"
          - name: DISABLE_STATS
            value: "1"
          - name: DISABLE_TRACING
            value: "1"
          - name: DD_PROFILING_ENABLED
            value: "true"
          - name: DD_LOGS_INJECTION
            value: "true"
          - name: DD_TRACE_SAMPLE_RATE
            value: "1"
          resources:
            requests:
              cpu: 100m
              memory: 64Mi
            limits:
              cpu: 200m
              memory: 128Mi
---
apiVersion: v1
kind: Service
metadata:
  name: checkoutservice
spec:
  type: ClusterIP
  selector:
    app: checkoutservice
  ports:
  - name: grpc
    port: 5050
    targetPort: 5050
