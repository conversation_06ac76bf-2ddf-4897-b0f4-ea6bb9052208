# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component
patchesStrategicMerge:
# cartservice - replace REDIS_ADDR to target new Memorystore (redis) instance
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: cartservice
  spec:
    template:
      spec:
        containers:
          - name: server
            env:
            - name: REDIS_ADDR
              value: "REDIS_CONNECTION_STRING"
# redis - remove the redis-cart Deployment
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: redis-cart
  $patch: delete
# redis - remove the redis-cart Service
- |-
  apiVersion: v1
  kind: Service
  metadata:
    name: redis-cart
  $patch: delete
