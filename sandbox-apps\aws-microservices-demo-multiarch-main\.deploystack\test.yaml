# Copyright 2021 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# DEPLOYSTACK - this file is the cloudbuild for running testing automatically
# in the testing rig

steps:
   - name: 'gcr.io/cloudshell-images/cloudshell:latest'
     entrypoint: bash
     args: [ '.deploystack/clean' ]
   - name: 'gcr.io/cloudshell-images/cloudshell:latest'
     entrypoint: bash
     args: [ '.deploystack/test' ]
timeout: 4200s
options:
  machineType: 'E2_HIGHCPU_8'