<% @body_id = 'checkout-page' %>

<div id="checkout" data-hook>
  <%= render partial: 'spree/shared/error_messages', locals: { target: @order } %>

  <div class="row align-items-center" data-hook="checkout_header">
    <div class="col-md-3 mb-4">
      <h1 class="mb-0" data-hook="checkout_title"><%= Spree.t(:checkout) %></h1>
    </div>
    <div class="col-md-9 mb-4" data-hook="checkout_progress">
      <%= checkout_progress %>
    </div>
  </div>

  <div class="row" data-hook="checkout_content">
    <div class="<%= if @order.state != 'confirm' then 'col-md-9' else 'col-md-12' end %>" data-hook="checkout_form_wrapper">
      <%= form_for @order, url: update_checkout_path(@order.state), html: { id: "checkout_form_#{@order.state}" } do |form| %>
        <% if @order.state == 'address' || !@order.email? %>
          <div class="card card-default mb-4">
            <div class="card-body">
              <div class="form-group">
                <%= form.label :email %>
                <%= form.email_field :email, class: 'form-control', required: true %>
              </div>
            </div>
          </div>
        <% end %>
        <%= form.hidden_field :state_lock_version %>
        <%= render @order.state, form: form %>
      <% end %>
    </div>
    <% if @order.state != 'confirm' %>
      <div id="checkout-summary" data-hook="checkout_summary_box" class="col-md-3">
        <%= render partial: 'summary', locals: { order: @order } %>
      </div>
    <% end %>
  </div>
</div>

<script>
  Spree.current_order_id = "<%= @order.number %>"
  Spree.current_order_token = "<%= @order.token %>"
</script>
