{{- if and .Values.frontend.create .Values.frontend.externalService }}
Note: It may take a few minutes for the LoadBalancer IP to be available.

Watch the status of the frontend IP address with:
    kubectl get --namespace {{ .Release.Namespace }} svc -w {{ .Values.frontend.name }}-external

Get the external IP address of the frontend:
    export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ .Values.frontend.name }}-external --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
    echo http://$SERVICE_IP
{{- end }}
{{- if .Values.frontend.virtualService.create }}
Get the external IP address of the ingress gateway:
    export SERVICE_IP=$(kubectl get svc --namespace {{ .Values.frontend.virtualService.gateway.namespace }} {{ .Values.frontend.virtualService.gateway.name }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
    echo http://$SERVICE_IP
{{- end }}
