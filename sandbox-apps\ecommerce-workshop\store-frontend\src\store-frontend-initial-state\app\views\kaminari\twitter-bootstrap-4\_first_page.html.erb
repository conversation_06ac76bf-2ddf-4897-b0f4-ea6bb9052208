<%# Link to the "First" page
  - available local variables
    url:           url to the first page
    current_page:  a page object for the currently displayed page
    total_pages:   total number of pages
    per_page:      number of items to fetch per page
    remote:        data-remote
-%>
<% unless current_page.first? %>
  <li class="first page-item">
    <%= link_to_unless current_page.first?, raw(t 'views.pagination.first'), url, remote: remote, class: 'page-link' %>
  </li>
<% end %>
