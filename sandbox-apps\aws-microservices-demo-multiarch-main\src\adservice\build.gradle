plugins {
    id 'com.google.protobuf' version '0.9.1'
    id 'com.github.sherter.google-java-format' version '0.9'
    id 'idea'
    id 'application'
}

repositories {
    mavenCentral()
    mavenLocal()
}

description = 'Ad Service'
group = "adservice"
version = "0.1.0-SNAPSHOT"

def grpcVersion = "1.51.1"
def jacksonCoreVersion = "2.14.1"
def jacksonDatabindVersion = "2.14.1"
def protocVersion = "3.21.12"

tasks.withType(JavaCompile) {
    sourceCompatibility = JavaVersion.VERSION_19
    targetCompatibility = JavaVersion.VERSION_19
}

ext {
    speed = project.hasProperty('speed') ? project.getProperty('speed') : false
    offlineCompile = new File("$buildDir/output/lib")
}

dependencies {
    if (speed) {
        implementation fileTree(dir: offlineCompile, include: '*.jar')
    } else {
        implementation "com.google.api.grpc:proto-google-common-protos:2.11.0",
                "javax.annotation:javax.annotation-api:1.3.2",
                "io.grpc:grpc-protobuf:${grpcVersion}",
                "io.grpc:grpc-stub:${grpcVersion}",
                "io.grpc:grpc-netty:${grpcVersion}",
                "io.grpc:grpc-services:${grpcVersion}",
                "io.grpc:grpc-census:${grpcVersion}",
                "org.apache.logging.log4j:log4j-core:2.19.0"

        runtimeOnly "com.fasterxml.jackson.core:jackson-core:${jacksonCoreVersion}",
                "com.fasterxml.jackson.core:jackson-databind:${jacksonDatabindVersion}",
                "io.netty:netty-tcnative-boringssl-static:2.0.54.Final"
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protocVersion}"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:${grpcVersion}"
        }
    }
    generateProtoTasks {
        all()*.plugins {
            grpc {}
        }
        ofSourceSet('main')
    }
}

googleJavaFormat {
    toolVersion '1.7'
}

// Inform IDEs like IntelliJ IDEA, Eclipse or NetBeans about the generated code.
sourceSets {
    main {
        java {
            srcDirs 'hipstershop'
            srcDirs 'build/generated/source/proto/main/java/hipstershop'
            srcDirs 'build/generated/source/proto/main/grpc/hipstershop'
        }
    }
}

startScripts.enabled = false

// This to cache dependencies during Docker image building. First build will take time.
// Subsequent build will be incremental.
task downloadRepos(type: Copy) {
    from configurations.compileClasspath
    into offlineCompile
    from configurations.compileClasspath
    into offlineCompile
}

task adService(type: CreateStartScripts) {
    mainClass.set('hipstershop.AdService')
    applicationName = 'AdService'
    outputDir = new File(project.buildDir, 'tmp')
    classpath = startScripts.classpath
    // defaultJvmOpts =
    //          ["-agentpath:/opt/cprof/profiler_java_agent.so=-cprof_service=adservice,-cprof_service_version=1.0.0"]
}

task adServiceClient(type: CreateStartScripts) {
    mainClass.set('hipstershop.AdServiceClient')
    applicationName = 'AdServiceClient'
    outputDir = new File(project.buildDir, 'tmp')
    classpath = startScripts.classpath
    // defaultJvmOpts =
    //          ["-agentpath:/opt/cprof/profiler_java_agent.so=-cprof_service=adserviceclient,-cprof_service_version=1.0.0"]
}

applicationDistribution.into('bin') {
    from(adService)
    from(adServiceClient)
    fileMode = 0755
}
