<% @body_id = 'product-details' %>
<% cache cache_key_for_product do %>
  <div data-hook="product_show" class="row" itemscope itemtype="https://schema.org/Product">
    <div class="col-lg-4 col-md-5" data-hook="product_left_part">
      <div data-hook="product_left_part_wrap">
        <div id="product-images" data-hook="product_images">
          <div id="main-image" class="card  " data-hook>
            <div class="card-body text-center">
              <%= product_image(@product, itemprop: "image") %>
            </div>
          </div>
          <div id="thumbnails" data-hook>
            <%= render partial: 'thumbnails' %>
          </div>
        </div>

        <div data-hook="product_properties">
          <%= render partial: 'properties' %>
        </div>

        <div data-hook="promotions">
          <%= render partial: 'promotions' %>
        </div>
      </div>
    </div>

    <div class="col-lg-8 col-md-7" data-hook="product_right_part">
      <div data-hook="product_right_part_wrap">
        <div id="product-description" data-hook="product_description">
          <h1 class="product-title mt-2" itemprop="name"><%= @product.name %></h1>

          <div itemprop="description" data-hook="description">
            <%= sanitize product_description(@product) %>
          </div>

          <div id="cart-form" data-hook="cart_form">
            <%= render partial: 'cart_form' %>
          </div>
        </div>

        <div id="taxon-crumbs" class="mt-4" data-hook="product_taxons">
          <%= render partial: 'taxons' %>
        </div>
      </div>
    </div>
  </div>
<% end %>
