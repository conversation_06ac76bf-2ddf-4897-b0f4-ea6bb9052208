<%= form_for :order, html: { id: 'add-to-cart-form' } do |f| %>
  <div class="row" id="inside-product-cart-form" data-hook="inside_product_cart_form" itemprop="offers" itemscope itemtype="https://schema.org/Offer">
    <% if @product.variants_and_option_values(current_currency).any? %>
      <div id="product-variants" class="col-lg-6 mt-4">
        <h3 class="product-section-title"><%= Spree.t(:variants) %></h3>
        <ul class="list-group">
          <% @product.variants_and_option_values(current_currency).each_with_index do |variant, index| %>
            <li class="list-group-item <%= "active" if index == 0 %>">
              <div class="form-check">
                <%= radio_button_tag "variant_id", variant.id, index == 0,
                    'data-price' => variant.price_in(current_currency).money,
                    'data-in-stock' => variant.can_supply?,
                    'data-backordered' => variant.backordered?,
                    class: 'form-check-input'
                %>
                <%= label_tag "variant_id_#{ variant.id }", class: "form-check-label" do %>
                  <span class="variant-description">
                    <%= variant.options_text %>
                  </span>
                  <% if variant_price variant %>
                    <span class="price diff"><%= variant_price variant %></span>
                  <% end %>
                  <% unless variant.can_supply? %>
                    <span class="out-of-stock"><%= Spree.t(:out_of_stock) %></span>
                  <% end %>
                <% end %>
              </div>
            </li>
          <% end%>
        </ul>
      </div>
    <% else %>
      <%= hidden_field_tag "variant_id", @product.master.id %>
    <% end %>

    <% if @product.price_in(current_currency) && !@product.price.nil? %>
      <div data-hook="product_price" class="col-lg-5 mt-4">
        <div id="product-price">
          <h3 class="product-section-title"><%= Spree.t(:price) %></h3>
          <div>
            <span class="lead price selling" itemprop="price" content="<%= @product.price_in(current_currency).amount.to_d %>">
              <%= display_price(@product) %>
            </span>
            <span itemprop="priceCurrency" content="<%= current_currency %>"></span>
          </div>

          <% if @product.master.can_supply? %>
            <link itemprop="availability" href="https://schema.org/InStock" />
          <% elsif @product.variants.empty? %>
            <span class="out-of-stock d-block mt-2">
              <%= Spree.t(:out_of_stock) %>
            </span>
          <% end %>
          <% if @product.backordered? %>
            <div class="alert alert-warning" id="cart-backordered-info">
              <%= Spree.t(:backordered_info) %>
            </div>
          <% end %>
        </div>

        <% if @product.can_supply? %>
          <div class="add-to-cart mt-2">
            <div class="input-group">
              <%= number_field_tag :quantity, 1, class: 'title form-control', min: 1 %>
              <div class="input-group-append">
                <%= button_tag class: 'btn btn-success', id: 'add-to-cart-button', type: :submit, disabled: true do %>
                  <%= Spree.t(:add_to_cart) %>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div id="product-price">
        <div>
          <span class="price selling" itemprop="price">
            <%= Spree.t('product_not_available_in_this_currency') %>
          </span>
        </div>
      </div>
    <% end %>
  </div>
<% end %>
