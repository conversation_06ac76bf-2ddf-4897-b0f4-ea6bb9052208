<?xml version="1.0" encoding="UTF-8"?>
<!--
 Copyright 2020 Google LLC

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<Configuration status="WARN">
  <Appenders>
    <Console name="STDOUT" target="SYSTEM_OUT">

      <!-- This is a JSON format that can be read by the Stackdriver Logging agent. The trace ID,
           span ID, sampling decision, and timestamp are interpreted by Stackdriver. It uses the
           special JSON keys that the Stackdriver Logging agent converts to "trace", "spanId",
           "traceSampled", and "timestamp" in the Stackdriver LogEntry
           (https://cloud.google.com/logging/docs/agent/configuration#special-fields). -->

      <JsonLayout compact="true" eventEol="true">
        <KeyValuePair key="logging.googleapis.com/trace" value="$${ctx:traceId}"/>
        <KeyValuePair key="logging.googleapis.com/spanId" value="$${ctx:spanId}"/>
        <KeyValuePair key="logging.googleapis.com/traceSampled" value="$${ctx:traceSampled}"/>
        <KeyValuePair key="time" value="$${date:yyyy-MM-dd}T$${date:HH:mm:ss.SSS}Z"/>
     </JsonLayout>

    </Console>
  </Appenders>
  <Loggers>
    <Logger name="io.grpc.netty" level="INFO"/>
    <Logger name="io.netty" level="INFO"/>
    <Logger name="sun.net" level="INFO"/>
    <Root level="TRACE">
      <AppenderRef ref="STDOUT"/>
    </Root>
  </Loggers>
</Configuration>
