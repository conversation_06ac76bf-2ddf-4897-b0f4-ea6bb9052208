<%= render partial: 'spree/shared/error_messages', locals: { target: @order } %>

<div class="table-responsive">
  <table class="table" id="cart-detail" data-hook>
    <thead>
      <tr class="active" data-hook="cart_items_headers">
        <th class="cart-item-description-header" colspan="2"><%= Spree.t(:item) %></th>
        <th class="cart-item-price-header"><%= Spree.t(:price) %></th>
        <th class="cart-item-quantity-header"><%= Spree.t(:qty) %></th>
        <th class="cart-item-total-header"><%= Spree.t(:total) %></th>
        <th class="cart-item-delete-header"></th>
      </tr>
    </thead>
    <tbody id="line_items" data-hook>
      <%= render partial: 'spree/orders/line_item', collection: order_form.object.line_items, locals: { order_form: order_form } %>
    </tbody>
    <% if @order.adjustments.nonzero.exists? || @order.line_item_adjustments.nonzero.exists? || @order.shipment_adjustments.nonzero.exists? || @order.shipments.any? %>
      <tr class="cart-subtotal">
        <td colspan="4" align='right'><h5><%= Spree.t(:cart_subtotal, count: @order.line_items.sum(:quantity)) %></h5></td>
        <td colspan>
          <h5>
            <%= order_form.object.display_item_total %>
          </h5>
        </td>
        <td></td>
      </tr>
      <%= render "spree/orders/adjustments" %>
    <% end %>
    <tr class="table-warning cart-total">
      <td colspan="4" align='right' class="align-middle">
        <h5 class="mb-0">
          <%= Spree.t(:total) %>
        </h5>
      </td>
      <td class="lead" colspan>
        <%= order_form.object.display_total %>
      </td>
      <td></td>
    </tr>
  </table>
</div>
