<%# Link to the "Previous" page
  - available local variables
    url:           url to the previous page
    current_page:  a page object for the currently displayed page
    total_pages:   total number of pages
    per_page:      number of items to fetch per page
    remote:        data-remote
-%>
<% unless current_page.first? %>
  <li class="prev page-item">
    <%= link_to_unless current_page.first?, raw(t 'views.pagination.previous'), url, rel: 'prev', remote: remote, class: 'page-link' %>
  </li>
<% end %>
