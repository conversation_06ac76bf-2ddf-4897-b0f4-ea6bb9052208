<% filters = @taxon ? @taxon.applicable_filters : [Spree::Core::ProductFilters.all_taxons] %>

<% unless filters.empty? %>
  <%= form_tag '', method: :get, id: 'sidebar_products_search' do %>
    <%= hidden_field_tag 'per_page', params[:per_page] %>
    <% filters.each do |filter| %>
      <% labels = filter[:labels] || filter[:conds].map {|m,c| [m,m]} %>
      <% next if labels.empty? %>
      <div 
        class="navigation mt-4"
        data-hook="navigation"
        aria-labelledby="products_search_wrapper"
        role="group">

        <h4 class="filter-title mt-0 h5" id="products_search_wrapper">
          <%= filter[:name] %>
        </h4>

        <ul class="list-group">
          <% labels.each do |nm,val| %>
            <% label = "#{filter[:name]}_#{nm}".gsub(/\s+/,'_') %>
            <li class="list-group-item">
              <div class="form-check">
                <input
                  aria-labelledby="products_search_wrapper"
                  type="checkbox"
                  class="form-check-input"
                  id="<%= label %>"
                  name="search[<%= filter[:scope].to_s %>][]"
                  value="<%= val %>"
                  <%= params[:search].present? &&
                    params[:search][filter[:scope]] &&
                    params[:search][filter[:scope]].include?(val.to_s) ?
                      "checked" :
                      "" %> />
                <label class="nowrap form-check-label" for="<%= label %>">
                  <%= nm %>
                </label>
              </div>
            </li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <%= submit_tag Spree.t(:search), name: nil, class: 'mt-4 btn btn-primary' %>
  <% end %>
<% end %>
