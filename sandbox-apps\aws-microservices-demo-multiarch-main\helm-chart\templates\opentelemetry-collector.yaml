{{- if .Values.opentelemetryCollector.create }}
{{- if .Values.serviceAccounts.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.opentelemetryCollector.name }}
  namespace: {{.Release.Namespace}}
  {{- if not .Values.serviceAccounts.annotationsOnlyForCartservice }}
  {{- with .Values.serviceAccounts.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
---
{{- end }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.opentelemetryCollector.name }}
  namespace: {{ .Release.Namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.opentelemetryCollector.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.opentelemetryCollector.name }}
    spec:
      {{- if .Values.serviceAccounts.create }}
      serviceAccountName: {{ .Values.opentelemetryCollector.name }}
      {{- else }}
      serviceAccountName: default
      {{- end }}
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 1000
        {{- if .Values.seccompProfile.enable }}
        seccompProfile:
          type: {{ .Values.seccompProfile.type }}
        {{- end }}
      {{- if eq .Values.opentelemetryCollector.projectId "PROJECT_ID" }}
      initContainers:
      # Init container retrieves the current cloud project id from the metadata server
      # and inserts it into the collector config template
      # https://cloud.google.com/compute/docs/storing-retrieving-metadata
      - name: otel-gateway-init
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - all
          privileged: false
          readOnlyRootFilesystem: true
        image: busybox:latest@sha256:3b3128d9df6bbbcc92e2358e596c9fbd722a437a62bafbc51607970e9e3b8869
        command:
        - '/bin/sh'
        - '-c'
        - |
           sed "s/PROJECT_ID/$(curl -H 'Metadata-Flavor: Google' http://metadata.google.internal/computeMetadata/v1/project/project-id)/" /template/collector-gateway-config-template.yaml >> /conf/collector-gateway-config.yaml
        volumeMounts:
        - name: collector-gateway-config-template
          mountPath: /template
        - name: collector-gateway-config
          mountPath: /conf
      {{- end }}
      containers:
      # This gateway container will receive traces and metrics from each microservice
      # and forward it to GCP
      - name: otel-gateway
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - all
          privileged: false
          readOnlyRootFilesystem: true
        args:
        - --config=/conf/collector-gateway-config.yaml
        image: otel/opentelemetry-collector-contrib:0.67.0@sha256:33393547ea1278af53629efcd8e682db1983f3176d7fca41016924417ce24988
        volumeMounts:
        - name: collector-gateway-config
          mountPath: /conf
      volumes:
      # Simple ConfigMap volume with template file
      - name: collector-gateway-config-template
        configMap:
          items:
          - key: collector-gateway-config-template.yaml
            path: collector-gateway-config-template.yaml
          name: collector-gateway-config-template
      # Create a volume to store the expanded template (with correct cloud project ID)
      - name: collector-gateway-config
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.opentelemetryCollector.name }}
  namespace: {{ .Release.Namespace }}
spec:
  ports:
  - name: grpc-otlp
    port: 4317
    protocol: TCP
    targetPort: 4317
  selector:
    app: {{ .Values.opentelemetryCollector.name }}
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: collector-gateway-config-template
  namespace: {{ .Release.Namespace }}
# Open Telemetry Collector config
# https://opentelemetry.io/docs/collector/configuration/
data:
  collector-gateway-config-template.yaml: |
    receivers:
      otlp:
        protocols: 
          grpc:
    processors:
    exporters:
      googlecloud:
        project: {{ .Values.opentelemetryCollector.projectId | quote }}
    service:
      pipelines:
        traces:
          receivers: [otlp] # Receive otlp-formatted data from other collector instances
          processors: []
          exporters: [googlecloud] # Export traces directly to Google Cloud
        metrics:
          receivers: [otlp]
          processors: []
          exporters: [googlecloud] # Export metrics to Google Cloud
{{- if .Values.networkPolicies.create }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ .Values.opentelemetryCollector.name }}
  namespace: {{ .Release.Namespace }}
spec:
  podSelector:
    matchLabels:
      app: {{ .Values.opentelemetryCollector.name }}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: {{ .Values.adService.name }}
    - podSelector:
        matchLabels:
          app: {{ .Values.cartService.name }}
    - podSelector:
        matchLabels:
          app: {{ .Values.checkoutService.name }}
    - podSelector:
        matchLabels:
          app: {{ .Values.currencyService.name }}
    - podSelector:
        matchLabels:
          app: {{ .Values.emailService.name }}
    - podSelector:
        matchLabels:
          app: {{ .Values.frontend.name }}
    - podSelector:
        matchLabels:
          app: {{ .Values.loadGenerator.name }}
    - podSelector:
        matchLabels:
          app: {{ .Values.paymentService.name }}
    - podSelector:
        matchLabels:
          app: {{ .Values.productCatalogService.name }}
    - podSelector:
        matchLabels:
          app: {{ .Values.recommendationService.name }}
    - podSelector:
        matchLabels:
          app: {{ .Values.shippingService.name }}
    ports:
     - port: 4317
       protocol: TCP
  egress:
  - {}
{{- end }}
{{- if .Values.sidecars.create }}
---
apiVersion: networking.istio.io/v1beta1
kind: Sidecar
metadata:
  name: {{ .Values.opentelemetryCollector.name }}
  namespace: {{ .Release.Namespace }}
spec:
  workloadSelector:
    labels:
      app: {{ .Values.opentelemetryCollector.name }}
  egress:
  - hosts:
    - istio-system/*
{{- end }}
{{- if .Values.authorizationPolicies.create }}
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ .Values.opentelemetryCollector.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.opentelemetryCollector.name }}
  rules:
  - from:
    - source:
        principals:
        {{- if .Values.serviceAccounts.create }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.adService.name }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.cartService.name }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.checkoutService.name }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.currencyService.name }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.emailService.name }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.frontend.name }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.loadGenerator.name }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.paymentService.name }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.productCatalogService.name }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.recommendationService.name }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.shippingService.name }}
        {{- else }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/default
        {{- end }}
    to:
    - operation:
        ports:
        - "4317"
{{- end }}
{{- end }}
