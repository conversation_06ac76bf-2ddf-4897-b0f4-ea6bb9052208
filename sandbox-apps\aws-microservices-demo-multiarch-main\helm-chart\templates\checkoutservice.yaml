{{- if .Values.checkoutService.create }}
{{- if .Values.serviceAccounts.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.checkoutService.name }}
  namespace: {{.Release.Namespace}}
  {{- if not .Values.serviceAccounts.annotationsOnlyForCartservice }}
  {{- with .Values.serviceAccounts.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
---
{{- end }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.checkoutService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.checkoutService.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.checkoutService.name }}
    spec:
      {{- if .Values.serviceAccounts.create }}
      serviceAccountName: {{ .Values.checkoutService.name }}
      {{- else }}
      serviceAccountName: default
      {{- end }}
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 1000
        {{- if .Values.seccompProfile.enable }}
        seccompProfile:
          type: {{ .Values.seccompProfile.type }}
        {{- end }}
      containers:
      - name: server
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - all
          privileged: false
          readOnlyRootFilesystem: true
        {{- if .Values.nativeGrpcHealthCheck }}
        image: {{ .Values.images.repository }}/{{ .Values.checkoutService.name }}:{{ .Values.images.tag | default .Chart.AppVersion }}-native-grpc-probes
        {{- else }}
        image: {{ .Values.images.repository }}/{{ .Values.checkoutService.name }}:{{ .Values.images.tag | default .Chart.AppVersion }}
        {{- end }}
        ports:
        - containerPort: 5050
        readinessProbe:
          {{- if .Values.nativeGrpcHealthCheck }}
          grpc:
            port: 5050
          {{- else }}
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:5050"]
          {{- end }}
        livenessProbe:
          {{- if .Values.nativeGrpcHealthCheck }}
          grpc:
            port: 5050
          {{- else }}
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:5050"]
          {{- end }}
        env:
        - name: PORT
          value: "5050"
        - name: PRODUCT_CATALOG_SERVICE_ADDR
          value: "{{ .Values.productCatalogService.name }}:3550"
        - name: SHIPPING_SERVICE_ADDR
          value: "{{ .Values.shippingService.name }}:50051"
        - name: PAYMENT_SERVICE_ADDR
          value: "{{ .Values.paymentService.name }}:50051"
        - name: EMAIL_SERVICE_ADDR
          value: "{{ .Values.emailService.name }}:5000"
        - name: CURRENCY_SERVICE_ADDR
          value: "{{ .Values.currencyService.name }}:7000"
        - name: CART_SERVICE_ADDR
          value: "{{ .Values.cartService.name }}:7070"
        {{- if .Values.opentelemetryCollector.create }}
        - name: COLLECTOR_SERVICE_ADDR
          value: "{{ .Values.opentelemetryCollector.name }}:4317"
        {{- end }}
        {{- if .Values.googleCloudOperations.tracing }}
        - name: ENABLE_TRACING
          value: "1"
        {{- end }}
        {{- if .Values.googleCloudOperations.profiler }}
        - name: ENABLE_PROFILER
          value: "1"
        {{- end }}
        resources:
          requests:
            cpu: 100m
            memory: 64Mi
          limits:
            cpu: 200m
            memory: 128Mi
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.checkoutService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  type: ClusterIP
  selector:
    app: {{ .Values.checkoutService.name }}
  ports:
  - name: grpc
    port: 5050
    targetPort: 5050
{{- if .Values.networkPolicies.create }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ .Values.checkoutService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  podSelector:
    matchLabels:
      app: {{ .Values.checkoutService.name }}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: {{ .Values.frontend.name }}
    ports:
     - port: 5050
       protocol: TCP
  egress:
  - {}
{{- end }}
{{- if .Values.sidecars.create }}
---
apiVersion: networking.istio.io/v1beta1
kind: Sidecar
metadata:
  name: {{ .Values.checkoutService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  workloadSelector:
    labels:
      app: {{ .Values.checkoutService.name }}
  egress:
  - hosts:
    - istio-system/*
    - ./{{ .Values.cartService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    - ./{{ .Values.currencyService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    - ./{{ .Values.emailService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    - ./{{ .Values.paymentService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    - ./{{ .Values.productCatalogService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    - ./{{ .Values.shippingService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    {{- if .Values.opentelemetryCollector.create }}
    - ./{{ .Values.opentelemetryCollector.name }}.{{ .Release.Namespace }}.svc.cluster.local
    {{- end }}
{{- end }}
{{- if .Values.authorizationPolicies.create }}
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ .Values.checkoutService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.checkoutService.name }}
  rules:
  - from:
    - source:
        principals:
        {{- if .Values.serviceAccounts.create }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.frontend.name }}
        {{- else }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/default
        {{- end }}
    to:
    - operation:
        paths:
        - /hipstershop.CheckoutService/PlaceOrder
        methods:
        - POST
        ports:
        - "5050"
{{- end }}
{{- end }}
