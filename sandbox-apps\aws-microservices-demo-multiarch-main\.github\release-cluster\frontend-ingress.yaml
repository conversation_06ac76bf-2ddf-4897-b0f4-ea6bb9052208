apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-ingress
  annotations:
    kubernetes.io/ingress.global-static-ip-name: online-boutique-ip
    networking.gke.io/managed-certificates: online-boutique-certificate
    networking.gke.io/v1beta1.FrontendConfig: frontend-frontend-config
spec:
  defaultBackend:
    service:
      name: frontend
      port:
        number: 80
  rules:
  - http:
      paths:
      - path: /*
        pathType: ImplementationSpecific
        backend:
          service:
            name: frontend
            port:
              number: 80
