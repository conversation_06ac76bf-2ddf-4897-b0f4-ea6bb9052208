{{- if .Values.recommendationService.create }}
{{- if .Values.serviceAccounts.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.recommendationService.name }}
  namespace: {{.Release.Namespace}}
  {{- if not .Values.serviceAccounts.annotationsOnlyForCartservice }}
  {{- with .Values.serviceAccounts.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
---
{{- end }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.recommendationService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.recommendationService.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.recommendationService.name }}
    spec:
      {{- if .Values.serviceAccounts.create }}
      serviceAccountName: {{ .Values.recommendationService.name }}
      {{- else }}
      serviceAccountName: default
      {{- end }}
      terminationGracePeriodSeconds: 5
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 1000
        {{- if .Values.seccompProfile.enable }}
        seccompProfile:
          type: {{ .Values.seccompProfile.type }}
        {{- end }}
      containers:
      - name: server
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - all
          privileged: false
          readOnlyRootFilesystem: true
        {{- if .Values.nativeGrpcHealthCheck }}
        image: {{ .Values.images.repository }}/{{ .Values.recommendationService.name }}:{{ .Values.images.tag | default .Chart.AppVersion }}-native-grpc-probes
        {{- else }}
        image: {{ .Values.images.repository }}/{{ .Values.recommendationService.name }}:{{ .Values.images.tag | default .Chart.AppVersion }}
        {{- end }}
        ports:
        - containerPort: 8080
        readinessProbe:
          periodSeconds: 5
          {{- if .Values.nativeGrpcHealthCheck }}
          grpc:
            port: 8080
          {{- else }}
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:8080"]
          {{- end }}
        livenessProbe:
          periodSeconds: 5
          {{- if .Values.nativeGrpcHealthCheck }}
          grpc:
            port: 8080
          {{- else }}
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:8080"]
          {{- end }}
        env:
        - name: PORT
          value: "8080"
        - name: PRODUCT_CATALOG_SERVICE_ADDR
          value: "{{ .Values.productCatalogService.name }}:3550"
        {{- if .Values.opentelemetryCollector.create }}
        - name: COLLECTOR_SERVICE_ADDR
          value: "{{ .Values.opentelemetryCollector.name }}:4317"
        {{- end }}
        {{- if .Values.googleCloudOperations.tracing }}
        - name: ENABLE_TRACING
          value: "1"
        {{- end }}
        {{- if not .Values.googleCloudOperations.profiler }}
        - name: DISABLE_PROFILER
          value: "1"
        {{- end }}
        resources:
          requests:
            cpu: 100m
            memory: 220Mi
          limits:
            cpu: 200m
            memory: 450Mi
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.recommendationService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  type: ClusterIP
  selector:
    app: {{ .Values.recommendationService.name }}
  ports:
  - name: grpc
    port: 8080
    targetPort: 8080
{{- if .Values.networkPolicies.create }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ .Values.recommendationService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  podSelector:
    matchLabels:
      app: {{ .Values.recommendationService.name }}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: {{ .Values.frontend.name }}
    ports:
     - port: 8080
       protocol: TCP
  egress:
  - {}
{{- end }}
{{- if .Values.sidecars.create }}
---
apiVersion: networking.istio.io/v1beta1
kind: Sidecar
metadata:
  name: {{ .Values.recommendationService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  workloadSelector:
    labels:
      app: {{ .Values.recommendationService.name }}
  egress:
  - hosts:
    - istio-system/*
    - ./{{ .Values.productCatalogService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    {{- if .Values.opentelemetryCollector.create }}
    - ./{{ .Values.opentelemetryCollector.name }}.{{ .Release.Namespace }}.svc.cluster.local
    {{- end }}
{{- end }}
{{- if .Values.authorizationPolicies.create }}
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ .Values.recommendationService.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.recommendationService.name }}
  rules:
  - from:
    - source:
        principals:
        {{- if .Values.serviceAccounts.create }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.frontend.name }}
        {{- else }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/default
        {{- end }}
    to:
    - operation:
        paths:
        - /hipstershop.RecommendationService/ListRecommendations
        methods:
        - POST
        ports:
        - "8080"
{{- end }}
{{- end }}
