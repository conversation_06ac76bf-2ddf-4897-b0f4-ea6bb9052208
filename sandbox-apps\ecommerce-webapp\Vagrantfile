# -*- mode: ruby -*-
# vi: set ft=ruby :

# Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.
# This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2020 Datadog, Inc.

Vagrant.configure(2) do |config|
  config.vm.box = "bento/ubuntu-20.04"

  config.vm.network :forwarded_port, guest: 8000, host: 8000, auto_correct: true
  config.vm.network :forwarded_port, guest: 8080, host: 8080, auto_correct: true
  config.vm.synced_folder "./data", "/data", create: true

  config.vm.provider "virtualbox" do |vb|
    vb.name = "Ecommerce Webapp"
    vb.customize ["modifyvm", :id, "--memory", "1024"]
  end

  config.vm.provision "shell", inline: "mkdir ~/data"
  config.vm.provision :file, source: './data', destination: '~/data'
  config.vm.provision :file, source: './setup.env', destination: '~/setup.env'
  config.vm.provision :file, source: './.require.vars.sh', destination: '~/.require.vars.sh'
  config.vm.provision :file, source: '../.setup.pre.sh', destination: '~/.setup.pre.sh'
  config.vm.provision :file, source: '../.setup.post.sh', destination: '~/.setup.post.sh'
  config.vm.provision "shell", path: "./setup.sh", privileged: false

end
