version: '3'
services:
  agent:
    image: "datadog/agent:7.29.0"
    environment:
      - DD_API_KEY
      - DD_APM_ENABLED=true
      - DD_LOGS_ENABLED=true
      - DD_LOGS_CONFIG_CONTAINER_COLLECT_ALL=true
      - DD_PROCESS_AGENT_ENABLED=true
      - DD_DOCKER_LABELS_AS_TAGS={"my.custom.label.team":"team"}
      - DD_TAGS='env:development'
    ports:
      - "8126:8126"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /proc/:/host/proc/:ro
      - /sys/fs/cgroup/:/host/sys/fs/cgroup:ro
    labels:
      com.datadoghq.ad.logs: '[{"source": "agent", "service": "agent"}]'
  discounts:
    environment:
      - FLASK_APP=discounts.py
      - FLASK_DEBUG=1
      - POSTGRES_PASSWORD
      - POSTGRES_USER
      - POSTGRES_HOST=db
      - DD_SERVICE=discounts-service
      - DD_AGENT_HOST=agent
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DD_PROFILING_ENABLED=true
      - DD_VERSION=1.1
    image: "ddtraining/discounts:2.1.0"
    ports:
      - "5001:5001"
      - "22"
    depends_on:
      - agent
      - db
    labels:
      com.datadoghq.ad.logs: '[{"source": "python", "service": "discounts-service"}]'
      my.custom.label.team: "discount"
  frontend:
    environment:
      - DD_AGENT_HOST=agent
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DD_SERVICE=store-frontend
      - DB_USERNAME
      - DB_PASSWORD
      - DD_VERSION=1.1
      - DD_CLIENT_TOKEN
      - DD_APPLICATION_ID
      - DD_ENV=development
      - DD_SITE=datadoghq.com
      - RAILS_HIDE_STACKTRACE=true
    image: "ddtraining/storefront-fixed:2.1.3"
    ports:
      - "3000:3000"
    depends_on:
      - agent
      - db
      - discounts
      - advertisements
    labels:
      com.datadoghq.ad.logs: '[{"source": "ruby", "service": "store-frontend"}]'
      my.custom.label.team: "frontend"
  nginx:
    image: "ddtraining/nginx:2.1.3"
    ports:
      - "80:80"
    depends_on:
      - frontend
    labels:
      com.datadoghq.ad.logs: '[{"source": "nginx", "service": "nginx"}]'
      my.custom.label.team: "nginx"
  advertisements:
    environment:
      - FLASK_APP=ads.py
      - FLASK_DEBUG=1
      - POSTGRES_PASSWORD
      - POSTGRES_USER
      - POSTGRES_HOST=db
      - DD_SERVICE=advertisements-service
      - DD_AGENT_HOST=agent
      - DD_LOGS_INJECTION=true
      - DD_TRACE_ANALYTICS_ENABLED=true
      - DD_PROFILING_ENABLED=true
      - DD_VERSION=1.0
    image: "ddtraining/advertisements-fixed:2.1.0"
    command: ddtrace-run flask run --port=5002 --host=0.0.0.0
    ports:
      - "5002:5002"
    depends_on:
      - agent
      - db
    labels:
      com.datadoghq.ad.logs: '[{"source": "python", "service": "ads-service"}]'
      my.custom.label.team: "advertisements"
  db:
    image: postgres:11-alpine
    restart: always
    environment:
      - POSTGRES_PASSWORD
      - POSTGRES_USER
    labels:
      com.datadoghq.ad.logs: '[{"source": "postgresql", "service": "postgres"}]'
  attackbox:
    image: "ddtraining/attackbox:2.1.3"
    logging:
      driver: none # this only works when using docker-compose (vs docker compose)
    environment:
      - ATTACK_GOBUSTER
      - ATTACK_HYDRA
      - ATTACK_GOBUSTER_INTERVAL
      - ATTACK_HYDRA_INTERVAL
      - ATTACK_SSH
      - ATTACK_SSH_INTERVAL
      - ATTACK_HOST
      - ATTACK_PORT
    depends_on:
      - discounts
      - frontend
      - nginx