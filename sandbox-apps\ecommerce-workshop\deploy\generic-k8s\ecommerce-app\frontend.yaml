apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    service: frontend
    app: ecommerce
    tags.datadoghq.com/env: "development"
  name: frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      service: frontend
      app: ecommerce
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        service: frontend
        app: ecommerce
        tags.datadoghq.com/env: "development"
    spec:
      containers:
      - args:
        - docker-entrypoint.sh
        command:
        - sh
        env:
        - name: DB_USERNAME
          value: user
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: pw
              name: db-password
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP
        - name: DD_LOGS_INJECTION
          value: "true"
        - name: DD_ENV
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['tags.datadoghq.com/env']
        - name: DD_ANALYTICS_ENABLED
          value: "true"
        # Enable RUM
        # - name: DD_CLIENT_TOKEN
        #   value: <your_client_token>
        # - name: DD_APPLICATION_ID
        #   value: <your_application_id>
        image: ddtraining/storefront:latest
        imagePullPolicy: Always
        name: ecommerce-spree-observability
        ports:
        - containerPort: 3000
          protocol: TCP
        resources:
          requests:
            cpu: 100m
            memory: 100Mi
          limits: {}
---
apiVersion: v1
kind: Service
metadata:
  labels:
    service: frontend
    app: ecommerce
  name: frontend
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 3000
      name: http
  selector:
    service: frontend
    app: ecommerce
  # Change this value to LoadBalancer for better public ingress routing to this
  # pod on most k8s platforms.
  type: ClusterIP
