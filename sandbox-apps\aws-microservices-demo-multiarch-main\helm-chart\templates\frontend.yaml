{{- if .Values.frontend.create }}
{{- if .Values.serviceAccounts.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.frontend.name }}
  namespace: {{.Release.Namespace}}
  {{- if not .Values.serviceAccounts.annotationsOnlyForCartservice }}
  {{- with .Values.serviceAccounts.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
---
{{- end }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.frontend.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.frontend.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.frontend.name }}
      annotations:
        sidecar.istio.io/rewriteAppHTTPProbers: "true"
    spec:
      {{- if .Values.serviceAccounts.create }}
      serviceAccountName: {{ .Values.frontend.name }}
      {{- else }}
      serviceAccountName: default
      {{- end }}
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 1000
        {{- if .Values.seccompProfile.enable }}
        seccompProfile:
          type: {{ .Values.seccompProfile.type }}
        {{- end }}
      containers:
        - name: server
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: true
          image: {{ .Values.images.repository }}/{{ .Values.frontend.name }}:{{ .Values.images.tag | default .Chart.AppVersion }}
          ports:
          - containerPort: 8080
          readinessProbe:
            initialDelaySeconds: 10
            httpGet:
              path: "/_healthz"
              port: 8080
              httpHeaders:
              - name: "Cookie"
                value: "shop_session-id=x-readiness-probe"
          livenessProbe:
            initialDelaySeconds: 10
            httpGet:
              path: "/_healthz"
              port: 8080
              httpHeaders:
              - name: "Cookie"
                value: "shop_session-id=x-liveness-probe"
          env:
          - name: PORT
            value: "8080"
          - name: PRODUCT_CATALOG_SERVICE_ADDR
            value: "{{ .Values.productCatalogService.name }}:3550"
          - name: CURRENCY_SERVICE_ADDR
            value: "{{ .Values.currencyService.name }}:7000"
          - name: CART_SERVICE_ADDR
            value: "{{ .Values.cartService.name }}:7070"
          - name: RECOMMENDATION_SERVICE_ADDR
            value: "{{ .Values.recommendationService.name }}:8080"
          - name: SHIPPING_SERVICE_ADDR
            value: "{{ .Values.shippingService.name }}:50051"
          - name: CHECKOUT_SERVICE_ADDR
            value: "{{ .Values.checkoutService.name }}:5050"
          - name: AD_SERVICE_ADDR
            value: "{{ .Values.adService.name }}:9555"
          - name: ENV_PLATFORM 
            value: {{ .Values.frontend.platform | quote }}
          {{- if .Values.opentelemetryCollector.create }}
          - name: COLLECTOR_SERVICE_ADDR
            value: "{{ .Values.opentelemetryCollector.name }}:4317"
          {{- end }}
          {{- if .Values.googleCloudOperations.tracing }}
          - name: ENABLE_TRACING
            value: "1"
          {{- end }}
          {{- if .Values.googleCloudOperations.profiler }}
          - name: ENABLE_PROFILER
            value: "1"
          {{- end }}
          - name: CYMBAL_BRANDING
            value: {{ .Values.frontend.cymbalBranding | quote }}
          - name: ENABLE_SINGLE_SHARED_SESSION
            value: {{ .Values.frontend.singleSharedSession | quote }}
          resources:
            requests:
              cpu: 100m
              memory: 64Mi
            limits:
              cpu: 200m
              memory: 128Mi
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.frontend.name }}
  namespace: {{ .Release.Namespace }}
spec:
  type: ClusterIP
  selector:
    app: {{ .Values.frontend.name }}
  ports:
  - name: http
    port: 80
    targetPort: 8080
{{- if .Values.frontend.externalService }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.frontend.name }}-external
  namespace: {{ .Release.Namespace }}
spec:
  type: LoadBalancer
  selector:
    app: {{ .Values.frontend.name }}
  ports:
  - name: http
    port: 80
    targetPort: 8080
{{- end }}
{{- if .Values.networkPolicies.create }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ .Values.frontend.name }}
  namespace: {{ .Release.Namespace }}
spec:
  podSelector:
    matchLabels:
      app: {{ .Values.frontend.name }}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  {{- if .Values.frontend.externalService }}
  - {}
  {{- else }}
  - from:
    - podSelector:
        matchLabels:
          app: {{ .Values.loadGenerator.name }}
    {{- if .Values.frontend.virtualService.create }}
    - namespaceSelector:
        matchLabels:
          name: {{ .Values.frontend.virtualService.gateway.namespace }}
      podSelector:
        matchLabels:
          {{ .Values.frontend.virtualService.gateway.labelKey }}: {{ .Values.frontend.virtualService.gateway.labelValue }}
    {{- end }}
    ports:
     - port: 8080
       protocol: TCP
  {{- end }}
  egress:
  - {}
{{- end }}
{{- if .Values.sidecars.create }}
---
apiVersion: networking.istio.io/v1beta1
kind: Sidecar
metadata:
  name: {{ .Values.frontend.name }}
  namespace: {{ .Release.Namespace }}
spec:
  workloadSelector:
    labels:
      app: {{ .Values.frontend.name }}
  egress:
  - hosts:
    - istio-system/*
    - ./{{ .Values.adService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    - ./{{ .Values.cartService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    - ./{{ .Values.checkoutService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    - ./{{ .Values.currencyService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    - ./{{ .Values.productCatalogService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    - ./{{ .Values.recommendationService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    - ./{{ .Values.shippingService.name }}.{{ .Release.Namespace }}.svc.cluster.local
    {{- if .Values.opentelemetryCollector.create }}
    - ./{{ .Values.opentelemetryCollector.name }}.{{ .Release.Namespace }}.svc.cluster.local
    {{- end }}
{{- end }}
{{- if .Values.authorizationPolicies.create }}
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ .Values.frontend.name }}
  namespace: {{ .Release.Namespace }}
spec:
  selector:
    matchLabels:
      app: {{ .Values.frontend.name }}
  rules:
  {{- if .Values.frontend.externalService }}
  - to:
  {{- else }}
  - from:
    - source:
        principals:
        {{- if .Values.serviceAccounts.create }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Values.loadGenerator.name }}
        {{- else }}
        - cluster.local/ns/{{ .Release.Namespace }}/sa/default
        {{- end }}
        {{- if .Values.frontend.virtualService.create }}
        - cluster.local/ns/{{ .Values.frontend.virtualService.gateway.namespace }}/sa/{{ .Values.frontend.virtualService.gateway.name }}
        {{- end }}
    to:
  {{- end }}  
    - operation:
        methods:
        - GET
        - POST
        ports:
        - "8080"
{{- end }}
{{- if .Values.frontend.virtualService.create }}
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ .Values.frontend.name }}
  namespace: {{ .Release.Namespace }}
spec:
  {{- with .Values.frontend.virtualService.hosts }}
  hosts:
  {{- toYaml . | nindent 2 }}
  {{- end }}
  gateways:
  - {{ .Values.frontend.virtualService.gateway.namespace }}/{{ .Values.frontend.virtualService.gateway.name }}
  http:
  - route:
    - destination:
        host: {{ .Values.frontend.name }}
        port:
          number: 80
{{- end }}
{{- end }}
